/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 06-06-2016.
 */

'use strict';

angular
    .module('scmApp')
    .controller('skuCtrl', [
        '$rootScope',
        '$scope',
        'authService',
        '$location',
        '$state',
        '$stateParams',
        'apiJson',
        'appUtil',
        '$http',
        '$toastService',
        'productService',
        'metaDataService',
        '$fileUploadService',
        'packagingService',
        '$timeout',
        'PrintService',
        '$alertService',
        'ScmApiService',
        'toast',
        function ($rootScope, $scope, authService, $location, $state, $stateParams, apiJson, appUtil,
                  $http, $toastService, productService, metaDataService, $fileUploadService, packagingService, $timeout, PrintService, $alertService, ScmApiService, toast) {

            $scope.skuDefinition = angular.copy($stateParams.skuDef);
            $scope.empType = angular.copy($stateParams.empType);
            $scope.editMode = appUtil.isEmptyObject($stateParams.skuDef) ? false : true;
            console.log($scope.skuDefinition);
            $scope.attributes = [];
            $scope.init = function () {
                $scope.minDisContinuedDate = appUtil.getDate(0);
                getAttrValues();
                $scope.assetCount = {};
                $scope.assetCount.count = 1;
                $scope.profileBaseUrl = apiJson.profileBaseUrl;

                // Initialize products arrays - will be populated by API call
                $scope.products = [];
                $scope.allProducts = [];
                getAllProductsBasicdetails(function() {
                    // This callback will be executed after products are loaded
                    handleEditModeAfterProductsLoad();
                });

                metaDataService.getCategoryAttributeMappings(function (mappings) {
                });

                $scope.categoryAttributeMappings = [];
                $scope.generateSKU = true;
                $scope.metaData = appUtil.getMetadata();
                $scope.uomMetadata = $scope.metaData.uomMetadata;
                $scope.inventoryList = $scope.metaData.inventoryList;
                $scope.editMode = false;
                $scope.taxCodes = appUtil.getTaxProfiles();
                $scope.displayIsBranded = false;
                $scope.documentId = null;

                if (appUtil.isEmptyObject($stateParams.skuDef)) {
                    $scope.skuDefinition = {
                        skuId: null,
                        skuName: null,
                        skuDescription: null,
                        supportsLooseOrdering: false,
                        creationDate: null,
                        createdBy: appUtil.createGeneratedBy(),
                        hasInner: false,
                        hasCase: false,
                        linkedProduct: null,
                        shelfLifeInDays: -1,
                        skuStatus: 'IN_ACTIVE',
                        unitOfMeasure: null,
                        skuImage: null,
                        unitPrice: null,
                        negotiatedUnitPrice: null,
                        priceLastUpdated: null,
                        torqusSkuName: null,
                        isDefault: false,
                        isBranded: false,
                        skuAttributes: [],
                        skuPackagings: [],
                        taxCode : null,
                        voDisContinuedFrom : null,
                        roDisContinuedFrom : null
                    };
                } else {
                    $scope.editMode = true;
                    $scope.skuDefinition = angular.copy($stateParams.skuDef);
                }


                metaDataService.getAttrDefinitions(function (attributes) {
                    var data = attributes;
                    for (var key in data) {
                        var subArray = data[key];
                        $scope.attributes = $scope.attributes.concat(subArray);
                    }
                    console.log($scope.attributes);
                });
            };

            function handleEditModeAfterProductsLoad() {
                if ($scope.skuDefinition && $scope.skuDefinition.linkedProduct && $scope.skuDefinition.linkedProduct.id) {
                    getProductDetailWithEditMode($scope.skuDefinition.linkedProduct.id);
                }
            }

            function getProductDetailWithEditMode(productId) {
                appUtil.getProductDetail(productId, function(productData) {
                    if (productData && $scope.editMode) {
                        // Set the complete product details
                        $scope.linkedProduct = productData;

                        // Set the product name code for display
                        $scope.linkProductNameCode = $scope.linkedProduct.productName + "-[" + $scope.linkedProduct.productCode + "]";

                        // Set attribute mapping with complete product data
                        $scope.setAttributeMapping($scope.linkedProduct);

                        // Edit mode specific initialization
                        $scope.generatedSkuName = $scope.skuDefinition.skuName;
                        $timeout(function () {
                            $scope.setDefaultSelects();
                        });
                        if (!appUtil.isEmptyObject($scope.skuDefinition.taxCode)) {
                            setTaxCode($scope.skuDefinition.taxCode);
                        }
                        if ($scope.skuDefinition.voDisContinuedFrom != null) {
                            $scope.skuDefinition.voDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.voDisContinuedFrom, "yyyy-MM-dd");
                        }
                        if ($scope.skuDefinition.roDisContinuedFrom != null) {
                            $scope.skuDefinition.roDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.roDisContinuedFrom, "yyyy-MM-dd");
                        }

                        console.log("Edit mode initialized:", $scope.skuDefinition);
                    }
                });
            }

            function getAllProductsBasicdetails(callback) {
                ScmApiService
                .get(apiJson.urls.productManagement.getProducts)
                .then(function (responseData) {
                    if(!appUtil.checkEmpty(responseData) && !appUtil.checkEmpty(responseData.data)) {
                        $scope.products = Object.values(responseData.data);
                        $scope.allProducts = $scope.products;
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                        if($scope.stateProductId != null && typeof $scope.selectProduct === 'function') {
                            $scope.selectProduct(null, $scope.stateProductId);
                        }
                    } else {
                        toast.warning("Products not found");
                        // Execute callback even if no products found
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    }
                })
            }

            $scope.selectProduct = function (product, productId) {
                var targetProductId = productId || (product && product.id);

                if (targetProductId) {
                    appUtil.getProductDetail(targetProductId, function(productData) {
                        if (productData) {
                            // Set the complete product details
                            $scope.linkedProduct = productData;

                            // Set the product name code for display
                            $scope.linkProductNameCode = $scope.linkedProduct.productName + "-[" + $scope.linkedProduct.productCode + "]";

                            // Set attribute mapping with complete product data
                            $scope.setAttributeMapping($scope.linkedProduct);
                        }
                    });
                } else if (product && product.productName) {
                    $scope.linkedProduct = product;
                    $scope.linkProductNameCode = product.productName + "-[" + product.productCode + "]";
                    $scope.setAttributeMapping(product);
                }
            };

            $scope.getAttributeName = function (id) {
                for (var i in $scope.attributes) {
                    if ($scope.attributes[i].attributeId == id) {
                        return $scope.attributes[i].attributeName;
                    }
                }
            };

            $scope.setVoDisContinuedFrom = function (voDate) {
                $scope.skuDefinition.voDisContinuedFrom = voDate;
            };

            $scope.setRoDisContinuedFrom = function (roDate) {
                $scope.skuDefinition.roDisContinuedFrom = roDate;
            };

            function getAttrValues() {
                metaDataService.getAttributeValues(function (values) {
                    $scope.attributeValueMap = values;
                });
            }

            $scope.selectOptions = {
                'width': '100%'
            };

            $scope.multiSelectOptions = {
                'width': '100%',
                'multiple': true
            };

            $scope.onTaxCategoryChange = function() {
                $scope.skuDefinition.taxCode = $scope.taxCategory.code;
            };

            $scope.setDefaultSelects = function () {
                if (!appUtil.isEmptyObject($scope.skuDefinition.skuAttributes)) {
                    for (var i = 0; i < $scope.categoryAttributeMappings.length; i++) {
                        var m = $scope.categoryAttributeMappings[i];
                        for (var j = 0; j < m.attributeValues.length; j++) {
                            var n = m.attributeValues[j];
                            for (var k = 0; k < $scope.skuDefinition.skuAttributes.length; k++) {
                                var o = $scope.skuDefinition.skuAttributes[k];
                                if (o.attributeId == n.attributeDefinitionId
                                    && o.attributeValueId == n.attributeValueId) {
                                    m.selectedAttribute = n;
                                    $('#' + m.attributeDefinition.name.replace('/', '')).val(n)
                                        .trigger('change');
                                }
                            }
                        }
                    }
                }
            }

            $scope.setAttributeMapping = function (product) {

                if($scope.editMode == undefined || $scope.editMode == null || !$scope.editMode){
                    $scope.skuDefinition.isBranded = false;
                }

                if (product == null || product == undefined) {
                    console.warn("setAttributeMapping called with null/undefined product");
                    return;
                }

                console.log("Setting attribute mapping for product:", product);
                $scope.linkedProduct = product;
                $scope.categoryAttributeMappings = [];

                // Ensure product has both name and productName for compatibility
                if (product.name && !product.productName) {
                    product.productName = product.name;
                } else if (product.productName && !product.name) {
                    product.name = product.productName;
                }

                $scope.skuDefinition.linkedProduct = {
                    id: product.id || product.productId,
                    code: "",
                    name: product.name || ""
                };

                // Safe tax code setting
                if (product.taxCode) {
                    setTaxCode(product.taxCode);
                } else {
                    console.warn("Product has no taxCode:", product.productId);
                }

                $scope.skuDefinition.shelfLifeInDays = product.shelfLifeInDays || -1;
                $scope.skuDefinition.unitOfMeasure = product.unitOfMeasure;
                if ($scope.metaData.categoryAttributeMappings != undefined
                    && $scope.metaData.categoryAttributeMappings[product.categoryDefinition.id] != null) {
                        $scope.metaData.categoryAttributeMappings[product.categoryDefinition.id]
                        .forEach(function (mapping) {
                            if (mapping.mappingStatus == "ACTIVE") {
                                if (appUtil.checkAttributeType(mapping.attributeDefinition.id,
                                    "CATEGORY")) {
                                    mapping.attributeValues = [];
                                    mapping.selectedAttribute = null;
                                    if ($scope.metaData.attributeValues[mapping.attributeDefinition.id] != null) {
                                        $scope.metaData.attributeValues[mapping.attributeDefinition.id]
                                            .forEach(function (av) {
                                                if (av.attributeValueStatus == "ACTIVE") {
                                                    if(mapping.attributeDefinition.code=='BRAND'){
                                                        $scope.displayIsBranded = true;
                                                    }
                                                    mapping.attributeValues.push(av);
                                                }
                                            });
                                        $scope.categoryAttributeMappings.push(mapping);
                                        $scope.categoryAttributeMappings.sort(function (a, b) {
                                            return a.mappingOrder - b.mappingOrder;
                                        });
                                    }
                                }
                            }
                        });
                    $scope.generatedSkuName = product.productName;
                    // $scope.skuDefinition.skuName =
                    // product.productName;
                }
                /*
                    In case of fixed asset get Mapping associated with Product(Profile)
                 */
                if (product.categoryDefinition.id == 3) {
                    if (product.profileId != null) {
                        getProfileAttributeMapping(product.profileId);
                        // set Inventory list to no list = 1
                        $scope.skuDefinition.inventoryList = 1;
                    } else {
                        $scope.skuDefinition.entityAttributeValueMappings = [];
                    }

                } else {
                    $scope.skuDefinition.entityAttributeValueMappings = [];
                }
            };

            function getProfileAttributeMapping(profileId) {
                $http({
                    url: apiJson.urls.profileManagement.profileAttributeMapping,
                    method: 'GET',
                    params: {
                        profileId: profileId
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    console.log($scope.attributeValueMap);
                    $scope.profileAttributeMapping = response.data;
                    $scope.skuDefinition.entityAttributeValueMappings = [];
                    var counter = 0;
                    for (var i in $scope.profileAttributeMapping) {
                        if ($scope.profileAttributeMapping[i].definedAtSKU && $scope.profileAttributeMapping[i].status == 'ACTIVE') {
                            console.log($scope.profileAttributeMapping[i]);
                            $scope.skuDefinition.entityAttributeValueMappings[counter] = generateEntityAttributeValueMapping($scope.profileAttributeMapping[i],
                                $scope.skuDefinition, $scope.attributeValueMap[$scope.profileAttributeMapping[i].attributeId]);
                            counter++;
                        }
                    }
                    if($scope.editMode == true){
                        getEntityAttributeValueMappingForSKU($scope.skuDefinition);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            function getEntityAttributeValueMappingForSKU(sku) {
                $http({
                    url: apiJson.urls.profileManagement.getEntityAttributeValueMappings,
                    method: 'GET',
                    params: {
                        entityId: sku.skuId,
                        entityType : "SKU"
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    console.log(response.data);
                    var existingMapping = response.data;
                    for(var i in $scope.skuDefinition.entityAttributeValueMappings){
                        var entity = $scope.skuDefinition.entityAttributeValueMappings[i];
                        for(var j in existingMapping){
                            if(entity.attributeId == existingMapping[j].attributeId){
                                entity.attributeValueId = existingMapping[j].attributeValueId;
                            }
                        }
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

            function setTaxCode(code){
                if (!code || !$scope.taxCodes) {
                    return;
                }

                $scope.taxCategory = null; // Reset first
                for(var i in $scope.taxCodes){
                    if($scope.taxCodes[i].code == code){
                        $scope.taxCategory = $scope.taxCodes[i];
                        break;
                    }
                }

                // Only set if taxCategory was found
                if ($scope.taxCategory && $scope.taxCategory.code) {
                    $scope.skuDefinition.taxCode = $scope.taxCategory.code;
                    $timeout(function (){
                        $('#taxCategory').trigger('change');
                    });
                }
            }

            function generateEntityAttributeValueMapping(profileAttributeMapping, sku, valueList) {
                var mapping = {};
                mapping.entityAttributeValueMappingId = null;
                mapping.profileId = profileAttributeMapping.profileId;
                mapping.attributeId = profileAttributeMapping.attributeId;
                mapping.attributeValueId = null;
                mapping.profileAttributeMappingId = profileAttributeMapping.profileAttributeMappingId;
                mapping.entityType = "SKU";
                mapping.entityId = null;
                mapping.creationDate = null;
                mapping.createdBy = appUtil.createGeneratedBy();
                mapping.status = "ACTIVE";
                mapping.valueList = valueList; // temporary property to store list of values associated with particular attribute
                mapping.isMandatory = profileAttributeMapping.mandatoryAtSKU;
                mapping.associatedImage = profileAttributeMapping.associatedImage;
                mapping.participateInName = profileAttributeMapping.participateInName;
                //mapping.isMandatory = true;
                return mapping;
            }

            function getProfileName(packagingObject) {
                var profile = packagingService.getProfile(packagingObject.packagingId);
                packagingObject.packagingName = profile.packagingName;
                packagingObject.subPackagingId = profile.subPackagingId;
            }

            $scope.updateSKUName = function () {
                var valid = true;
                console.log($scope.skuDefinition.entityAttributeValueMappings);
                console.log($scope.categoryAttributeMappings);
                //     if (mapping.mandatoryAtSKU && mapping.attributeValueId == null) {
                //         valid = false;
                //         $toastService.create("Please select " + mapping.attributeDefinition.name);
                //         return false;
                //     }
                // });/ $scope.skuDefinition.entityAttributeValueMappings.forEach(function (mapping) {

                if (valid) {
                    var name = $scope.generatedSkuName;
                    $scope.skuDefinition.entityAttributeValueMappings.forEach(function (mapping) {
                        if (mapping.participateInName == true) {
                            var selectedAttribute = mapping.attributeValueId;
                            if (selectedAttribute != null) {
                                var i;
                                for (i = 0; i < mapping.valueList.length; i++) {
                                    if (mapping.valueList[i].attributeValueId == selectedAttribute)
                                        name += " " + mapping.valueList[i].attributeValue;
                                }
                            }
                        }

                    });
                    $scope.generatedSkuName = name;
                    // $scope.skuDefinition.skuName = name;
                }
            };


            $scope.generateSKUName = function () {
                var valid = true;
                // console.log($scope.categoryAttributeMappings);
                $scope.categoryAttributeMappings.forEach(function (mapping) {

                    if(mapping.attributeDefinition.code=='BRAND'){
                        if(mapping.selectedAttribute == null){
                            $scope.skuDefinition.brand = null;
                        }else {
                            $scope.skuDefinition.brand = mapping.selectedAttribute.attributeValue;
                        }
                    }

                    if (mapping.mandatory && mapping.selectedAttribute == null) {
                        valid = false;
                        $toastService.create("Please select " + mapping.attributeDefinition.name);
                        return false;
                    }
                });
                if (valid) {
                    var name = $scope.linkedProduct != undefined && $scope.linkedProduct != null ? $scope.linkedProduct.productName
                        : "";
                    $scope.categoryAttributeMappings.forEach(function (mapping) {
                        var selectedAttribute = mapping.selectedAttribute;
                        if (selectedAttribute != null && mapping.usedInNaming) {
                            name += " " + selectedAttribute.attributeValue;
                        }
                    });
                    $scope.generatedSkuName = name;
                    // $scope.skuDefinition.skuName = name;
                }
                if ($scope.linkedProduct.categoryDefinition.id == 3) {
                    $scope.updateSKUName();
                }
            };

            $scope.setInner = function () {
                if ($scope.skuDefinition.hasCase) {
                    $scope.skuDefinition.hasInner = true;
                }
            };

            $scope.fun = function (str) {
                $scope.augmentskuname = $scope.augmentskuname + str;
            }

            $scope.setCase = function () {
                if (!$scope.skuDefinition.hasInner) {
                    $scope.skuDefinition.hasCase = false;
                }
            };

            $scope.uploadDoc = function(type) {
                if(type == 'IMAGE') {
                    $fileUploadService.openFileModal("Upload Sku Image", "Find", function (file) {
                        if (file == null) {
                            $toastService.create('File cannot be empty');
                            return;
                        }
                        if (file.size > 2048000) {
                            $toastService.create('File size should not be greater than 2 Mb');
                            return;
                        }
                        var fileExt = metaDataService.getFileExtension(file.name);
                        if (appUtil.isImage(fileExt.toLowerCase())) {
                            metaDataService.uploadGenericFile("OTHERS","ACCOUNT",file,"SKU_IMAGE", "SKU_DOCUMENT", function(doc){
                                $scope.documentId = doc.documentId;
                                $scope.skuDefinition.skuImage = $scope.documentId;
                            });
                        } else {
                            $toastService.create('Invalid file Ext.' + 'Supported format jpg, jpeg, png');
                        }
                    });
                } else {
                    $fileUploadService.openFileModal("Upload SKU Image", "Find", function (file) {
                        if (file == null) {
                            $toastService.create('File cannot be empty');
                            return;
                        }
                        metaDataService.uploadGenericFile("OTHERS","APPROVAL_OF_HOD",file,"SKU_APPROVAL_OF_HOD", "SKU_APPROVAL_OF_HOD_DOCUMENT", function(doc){
                            $scope.otherDocumentId = doc.documentId;
                            $scope.skuDefinition.approvalDocId = $scope.otherDocumentId;
                        });
                    });
                }
            }

            $scope.downloadDoc = function(id) {
                if (id === null) {
                    $toastService.create("Document ID is Null");
                    return;
                }
                metaDataService.downloadDocumentById(id);
            }

            $scope.createSKU = function (status) {
                $scope.skuDefinition.unitPrice=0;
               $scope.skuDefinition.negotiatedUnitPrice=0;
               console.log("sku new",$scope.skuDefinition)
                var valid = true;
                if ($scope.linkedProduct.shelfLifeInDays == -1 && $scope.skuDefinition.shelfLifeInDays != -1) {
                    $toastService.create("Shelf Life of the sku cannot be set as there is no shelf life of the linked product");
                    return false;
                }
                if ($scope.skuDefinition.isBranded != ($scope.skuDefinition.brand!=null)) {
                    $toastService.create("Please select brand and is branded check simultaneously");
                    return false;
                }
                if ($scope.linkedProduct.categoryDefinition.id == 3) {
                    for (var index in $scope.skuDefinition.entityAttributeValueMappings) {
                        if ($scope.skuDefinition.entityAttributeValueMappings[index].isMandatory
                            && $scope.skuDefinition.entityAttributeValueMappings[index].attributeValueId == null) {
                            $toastService.create("Please Select Attribute Value for "
                                + $scope.getAttributeName($scope.skuDefinition.entityAttributeValueMappings[index].attributeId));
                            return;
                        }
                    }
                }
                if ($scope.linkedProduct.categoryDefinition.id == 3) {
                    $scope.categoryAttributeMappings.forEach(function (mapping) {
                        if (mapping.mandatory && mapping.selectedAttribute == null) {
                            valid = false;
                            $toastService.create("Please select " + mapping.attributeDefinition.name);
                            return false;
                        }
                    });
                    if($scope.skuDefinition.inventoryList != 1){
                        $toastService.create("No list should be selected in inventory list for fixed assets sku");
                        return false
                    }
                }

                if (valid) {
                    $scope.skuDefinition.skuAttributes = [];
                    $scope.categoryAttributeMappings.forEach(function (mapping) {
                        var selectedAttr = mapping.selectedAttribute;
                        if (selectedAttr != null) {
                            $scope.skuDefinition.skuAttributes.push({
                                skuAttributeValueId: null,
                                skuId: null,
                                attributeId: selectedAttr.attributeDefinitionId,
                                attributeValueId: selectedAttr.attributeValueId,
                                mappingStatus: "ACTIVE"
                            });
                        }
                    });
                    $scope.skuDefinition.skuName = $scope.generatedSkuName;
                    if ($scope.skuDefinition.voDisContinuedFrom != null && $scope.skuDefinition.voDisContinuedFrom != "") {
                        $scope.skuDefinition.voDisContinuedFrom = new Date($scope.skuDefinition.voDisContinuedFrom);
                    }
                    if ($scope.skuDefinition.roDisContinuedFrom != null && $scope.skuDefinition.roDisContinuedFrom != "") {
                        $scope.skuDefinition.roDisContinuedFrom = new Date($scope.skuDefinition.roDisContinuedFrom);
                    }
                    console.log($scope.skuDefinition);
                    var url = "";
                    if($scope.empType == null) {
                        url = apiJson.urls.productManagement.addSkuDetail;
                        if ($scope.editMode) {
                            $scope.skuDefinition.skuStatus = null;
                            url = apiJson.urls.productManagement.updateSkuDetail;
                        }
                    } else if($scope.empType == 'USER') {
                        $scope.skuDefinition.skuStatus = 'INITIATED';
                        url = apiJson.urls.productManagement.addSkuDetailV2;
                    } else {
                        if(status == 'REJECT') {
                            $scope.skuDefinition.skuStatus = 'REJECTED';
                        } else {
                            $scope.skuDefinition.skuStatus = 'ACTIVE';
                        }
                        url = apiJson.urls.productManagement.updateSkuDetailV2;
                    }
                    sendRequest('POST', url, $scope.skuDefinition, $scope.editMode);
                }
            };

            function sendRequest(method, url, data, editMode) {
                console.log("Definition being passed :::", data);
                $alertService.confirm("Are you sure ?", "", function(result) {
                    if(result) {
                        $http({
                            method: method,
                            url: url,
                            data: data
                        }).then(
                            function success(response) {
                                console.log(response);
                                var status = editMode ? "updated" : "created";
                                var message = "";
                                if (response.data != null && response.status == 200) {
                                    message = "SKU " + status + " successfully";
                                    if($scope.empType == null) {
                                        !editMode ? appUtil.addSkuToCache(response.data) : appUtil
                                            .updateSkuInCache(response.data);
                                    } else if ($scope.empType == 'FINANCE' && response.data.skuStatus == 'ACTIVE') {
                                        appUtil.addSkuToCache(response.data);
                                    }
                                    $scope.goBack(response.data.skuId);
                                    return;
                                } else {
                                    message = "SKU could not be " + status;
                                    if ($scope.skuDefinition.voDisContinuedFrom != null) {
                                        $scope.skuDefinition.voDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.voDisContinuedFrom, "yyyy-MM-dd");
                                    }
                                    if ($scope.skuDefinition.roDisContinuedFrom != null) {
                                        $scope.skuDefinition.roDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.roDisContinuedFrom, "yyyy-MM-dd");
                                    }
                                }
                                $toastService.create(message, function () {
                                    if (response.data != null && response.status == 200) {
                                        $scope.init();
                                    }
                                });
                            }, function error(response) {
                                $alertService.alert("Error : ", response.data.errorMessage);
                                console.log("error:" , response);
                                if ($scope.skuDefinition.voDisContinuedFrom != null) {
                                    $scope.skuDefinition.voDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.voDisContinuedFrom, "yyyy-MM-dd");
                                }
                                if ($scope.skuDefinition.roDisContinuedFrom != null) {
                                    $scope.skuDefinition.roDisContinuedFrom = appUtil.formatDate($scope.skuDefinition.roDisContinuedFrom, "yyyy-MM-dd");
                                }
                        });
                    }
                });
            }

            $scope.goBack = function (skuId) {
                if($scope.empType == null) {
                    $state.go('menu.skuList', {
                        sku: skuId
                    });
                } else {
                    $state.go('menu.skuDashboard');
                }
            };
        }]).controller(
    'skuListCtrl',
    [
        '$rootScope',
        '$scope',
        'authService',
        '$location',
        '$state',
        'apiJson',
        'appUtil',
        '$http',
        '$toastService',
        'productService',
        'metaDataService',
        'packagingService',
        '$fileUploadService',
        'PrintService',
        '$alertService',
        'ScmApiService', 'toast',
        function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http,
                  $toastService, productService, metaDataService, packagingService, $fileUploadService, PrintService ,
                  $alertService, ScmApiService, toast) {


            $scope.products = [];
            $scope.skuListByProduct = {};
            $scope.productId = null;
            $scope.skuBaseUrl = apiJson.skuBaseUrl;
            $scope.skuList = [];
            $scope.attributes = [];
            $scope.selectOptions = {
                'width': '100%',
            };
            $scope.attributeValues = [];

            $scope.getProfile = packagingService.getProfile;

            $scope.init = function () {
                var elem = document.getElementById('modalUniqueValue');
                // getAttrValues();
                $scope.reset();
                packagingService.getAllProfiles();
                $scope.scmProducts = appUtil.getActiveScmProducts();
                $scope.searchText = "";
                $scope.filteredProducts = [];
                $scope.allProducts = [];
                getAllProductsBasicdetails();

                productService.getSkuList(function (skus) {
                    $scope.skuListByProduct = skus;
                    if ($scope.productId != null) {
                        $scope.selectProduct(null, $scope.productId);
                    }
                });

                metaDataService.getAttributeValues(function (values) {
                    $scope.attributeValueMap = values;
                    for (var index in values) {
                        var attrList = values[index];
                        attrList.forEach(function (attr) {
                            $scope.attributeValues[attr.attributeValueId] = attr.attributeValue + " ["
                                + attr.attributeValueShortCode + "]";
                        });
                    }
                });
                metaDataService.getAttrDefinitions(function (attributes) {
                    var data = attributes;
                    for (var key in data) {
                        var subArray = data[key];
                        $scope.attributes = $scope.attributes.concat(subArray);
                    }
                });
                // productService.getAllProducts(function (products) {
                //     $scope.products = products;
                // });
            };

            function getAllProductsBasicdetails(callback) {
                ScmApiService
                .get(apiJson.urls.productManagement.getProducts)
                .then(function (responseData) {
                    if(!appUtil.checkEmpty(responseData) && !appUtil.checkEmpty(responseData.data)) {
                        $scope.products = Object.values(responseData.data);
                        $scope.allProducts = $scope.products;

                        // Execute callback after products are loaded
                        if (callback && typeof callback === 'function') {
                            callback();
                        }

                        if($scope.stateProductId != null) {
                            $scope.selectProduct(null, $scope.stateProductId);
                        }
                    } else {
                        toast.warning("Products not found");
                        // Execute callback even if no products found
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    }
                })
                .catch(function(error) {
                    console.error("Error loading products:", error);
                    toast.error("Failed to load products");
                    // Execute callback even on error
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                });
            }

            $scope.filterProducts = function () {
                const query = $scope.searchText.toLowerCase();
                $scope.filteredProducts = $scope.products.filter(function (product) {
                    return product.name.toLowerCase().indexOf(query) !== -1;
                });
            };

            // function getAttrValues() {
            //     metaDataService.getAttributeValues(function (values) {
            //         $scope.attributeValueMap = values;
            //     });
            // }

            $scope.reset = function () {
                $scope.products = [];
                $scope.skuListByProduct = {};
                $scope.productId = null;
                $scope.skuList = [];
            };

            $scope.selectProduct = function (product, productId) {
                $scope.productId = productId;
                $scope.searchText = product != null ? product.name : "";
                $scope.filteredProducts = [];
                getProductDetail(productId);
                // $scope.products.map(function (mapping) {
                //     if (mapping.productId == productId) {
                //         $scope.selectedProduct = mapping;
                //     }
                // })
                $scope.skuList = $scope.skuListByProduct[productId];
            };
            function getProductDetail(productId) {

                ScmApiService
                    .get(apiJson.urls.productManagement.productDetail, {productId: productId})
                    .then(function (data) {
                        if(appUtil.checkEmpty(data)){
                            toast.warning("Product not found");
                            return null;
                        }
                        $scope.selectedProduct = data;
                    }
                );
                return null;
            }

            $scope.getAttributeName = function (id) {
                for (var i in $scope.attributes) {
                    if ($scope.attributes[i].attributeId == id) {
                        return $scope.attributes[i].attributeName;
                    }
                }
            };

            $scope.setValueForAll = function(fieldName, fieldValue, isInternal, internalFieldValue) {
                if(isInternal) {
                    for(var index in $scope.assetList) {
                        if($scope.assetList[index].assetStatus == 'INITIATED' && $scope.assetList[index].selected == true){
                            for(var i in $scope.assetList[index].entityAttributeValueMappings) {
                                if($scope.assetList[index].entityAttributeValueMappings[i].attributeId == internalFieldValue ) {
                                    $scope.assetList[index].entityAttributeValueMappings[i].attributeValueId = fieldValue;
                                }
                            }
                        }
                    }
                } else {
                    for(var index in $scope.assetList) {
                        if($scope.assetList[index].assetStatus == 'INITIATED' && $scope.assetList[index].selected == true){
                            $scope.assetList[index][fieldName] = fieldValue;
                        }
                    }
                }

            };

            $scope.validateForDeactivation = function (event, sku, activate){
                $http({
                    method: 'GET',
                    url: apiJson.urls.scmMetadata.validateForDeactivation,
                    params: { id : sku.skuId,
                    type : "SKU"}
                }).then(function success(response) {
                    console.log("Got response ::::", response.data);
                    if(response.data.canBeDeActivated === true){
                        $scope.changeStatus(event,sku,activate);
                    }else{
                        var msg = response.data.message;
                        $alertService.alert("Can't Deactivate" , msg , false);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }


            $scope.changeStatus = function (event, sku, activate) {
                var url = activate ? apiJson.urls.productManagement.activateSKU
                    : apiJson.urls.productManagement.deactivateSKU;
                $http({
                    method: 'PUT',
                    url: url,
                    data: sku.skuId
                }).then(function success(response) {
                    console.log("Got response ::::", response.data);
                    var msg = "Got some error during updating status";
                    if (response != undefined && response.data) {
                        sku.skuStatus = activate ? "ACTIVE" : "IN_ACTIVE";
                        msg = "Status update successfully";
                        appUtil.updateSkuInCache(sku);
                    } else {
                        msg = "Couldn\'t update status. Please try again later";
                    }
                    $toastService.create(msg);
                }, function error(response) {
                    console.log("error:" + response);
                });
                event.stopPropagation();
            };

            $scope.editSkuData = function (event, skuDetail) {
                $state.go('menu.addSKU', {skuDef: skuDetail});
            };

            $scope.registerAsset = function (sku) {
                console.log(sku);
                console.log($scope.productId);
                $scope.selectedSKU = sku;
                if ($scope.selectedProduct.profileId == null) {
                    $toastService.create('There seems to be no profile attached to product, Please attach the profile to product ' + $scope.selectedProduct.productName);
                    return;
                }
                $http({
                    method: 'GET',
                    url: apiJson.urls.profileManagement.getProfile,
                    params: {
                        profileId: $scope.selectedProduct.profileId
                    }
                }).then(function success(response) {
                    $scope.selectedProfile = response.data;
                    getProfileAttributeMappingsAtAssetLevel($scope.selectedProfile.profileId);

                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create('Error getting profile associated with Product ' + $scope.selectedProduct.productName);
                });
            };

            function getProfileAttributeMappingsAtAssetLevel(profileId) {
                $http({
                    method: 'GET',
                    url: apiJson.urls.profileManagement.profileAttributeMappingAsset,
                    params: {
                        profileId: profileId
                    }
                }).then(function success(response) {
                    console.log(response.data);
                    $scope.profileAttributeMappingList = response.data;
                    if ($scope.selectedProduct.bulkGRAllowed == true) {
                        $scope.openCountModal();
                    } else {
                        $scope.assetCount = {};
                        $scope.assetCount.count = 1;
                        if ($scope.selectedProfile.uniqueFieldName == null && $scope.profileAttributeMappingList.length == 0) {
                            $scope.createAssetList();
                            $scope.addBackDatedAsset($scope.assetList[0]); // create asset if there is no need to enter any input
                        } else {
                            $scope.createAssetList();
                            openModal();
                        }
                    }

                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create('Error getting profile attribute  associated with Product ' + $scope.selectedProduct.productName);
                });

            }

            $scope.getAttributeName = function (id) {
                for (var i in $scope.attributes) {
                    if ($scope.attributes[i].attributeId == id) {
                        return $scope.attributes[i].attributeName;
                    }
                }
            };

            $scope.createAssetList = function () {
                console.log($scope.assetCount);
                $scope.assetList = [];
                for (var index = 0; index < $scope.assetCount.count; index++) {
                    var asset = createAssetObj();
                    var profileAttributeMapping = $scope.profileAttributeMappingList;
                    var validProfileAttributeMapping = [];
                    for (var i in profileAttributeMapping) {
                        if (profileAttributeMapping[i].status == 'ACTIVE') {
                            validProfileAttributeMapping.push(profileAttributeMapping[i]);
                            var valueList = angular.copy($scope.attributeValueMap[profileAttributeMapping[i].attributeId])
                            profileAttributeMapping[i].valueList = valueList;
                            asset.entityAttributeValueMappings[i] = generateEntityAttributeValueMapping(profileAttributeMapping[i],
                                valueList);
                        }
                    }
                    asset.profileAttributeMappingList = validProfileAttributeMapping;
                    $scope.assetList.push(asset);
                }
                console.log($scope.assetList);

            };

            function createAssetObj() {
                var assetObj = {}
                assetObj.productId = $scope.selectedProduct.productId;
                assetObj.profileId = $scope.selectedProfile.profileId;
                assetObj.skuId = $scope.selectedSKU.skuId;
                assetObj.unitId = appUtil.getUnitData().id;
                assetObj.uniqueFieldName = $scope.selectedProfile.uniqueFieldName;
                assetObj.uniqueFieldValue = null;
                assetObj.lastTagPrintedBy = appUtil.createGeneratedBy();
                assetObj.createdBy = appUtil.createGeneratedBy();
                assetObj.entityAttributeValueMappings = [];
                assetObj.assetStatus = 'INITIATED'; // assets created are not send to initiated state they are send in created created state
                // this has been added here just to help with UI
                return assetObj;
            }

            $scope.openCountModal = function () {
                var modal = document.getElementById("_assetCountModal");
                modal.style.display = "block";
            };

            $scope.closeCountModal = function () {
                var modal = document.getElementById("_assetCountModal");
                modal.style.display = "none";
                window.onclick = function (event) {
                    if (event.target == modal) {
                        modal.style.display = "none";
                    }
                }
            };

            $scope.selectAll = function (value) {
                for(var index in $scope.assetList){
                    if($scope.assetList[index].assetStatus == 'INITIATED'){
                        $scope.assetList[index].selected = value;
                    }
                }
            };

            function openModal() {
                var modal = document.getElementById("myModal");
                modal.style.display = "block";
            }

            function closeModal() {
                var modal = document.getElementById("myModal");
                modal.style.display = "none";
                window.onclick = function (event) {
                    if (event.target == modal) {
                        modal.style.display = "none";
                    }
                }
            }

            $scope.createAssets = function () {

                if ($scope.assetCount.count <= 0) {
                    $toastService.create('Please Enter number more than zero');
                    return;
                }
                $scope.closeCountModal();
                $scope.createAssetList();
                openModal();
            };


            $scope.bulkGenerate = function(){
                for(var index in $scope.assetList) {
                    if($scope.assetList[index].assetStatus == 'INITIATED'){
                        $scope.addBackDatedAsset($scope.assetList[index]);
                    }
                }
            };

            function validateEnteredValue(asset) {

                if((asset.uniqueFieldName != null && asset.uniqueFieldName.length > 0)
                    && (asset.uniqueFieldValue == null || asset.uniqueFieldValue.length == 0)){
                    $toastService.create("Please enter unique Field value First");
                    return true;
                }
                for(var index in asset.entityAttributeValueMappings) {
                    if(asset.entityAttributeValueMappings[index].isMandatory
                        && asset.entityAttributeValueMappings[index].attributeValueId == null && asset.entityAttributeValueMappings[index].standAlone == false){
                        $toastService.create("Please Select Attribute Value for "
                            + $scope.getAttributeName(asset.entityAttributeValueMappings[index].attributeId));
                        return true;
                    }
                }
                for(var index in asset.entityAttributeValueMappings) {
                    if((asset.entityAttributeValueMappings[index].attributeValue == null || asset.entityAttributeValueMappings[index].attributeValue.length == 0)
                        && asset.entityAttributeValueMappings[index].standAlone == true){
                        $toastService.create("Please Enter Attribute Value for "
                            + $scope.getAttributeName(asset.entityAttributeValueMappings[index].attributeId));
                        return true;
                    }
                }
            }

            $scope.addBackDatedAsset = function (asset) {
                if(validateEnteredValue(asset)){
                    return;
                }
                console.log('asset to be generated' , asset);
                $http({
                    method: 'POST',
                    url: apiJson.urls.assetManagement.createBackDatedAsset,
                    data: asset
                }).then(function success(response) {

                    $scope.createdAsset = response.data;
                    var code = {}
                    asset.assetStatus = "CREATED"
                    code.assetName = $scope.createdAsset.assetName;
                    for(var i in $scope.scmProducts){
                        if($scope.scmProducts[i].productId == $scope.createdAsset.productId){
                            code.subCategoryCode = $scope.scmProducts[i].subCategoryDefinition.code;
                        }
                    }
                    code.assetTagValue = $scope.createdAsset.tagValue
                    $toastService.create('Asset Created successfully with Tag Value - ' + $scope.createdAsset.tagValue);
                    PrintService.printBarCode(code);
                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create(response);
                });
            };

            function generateEntityAttributeValueMapping(profileAttributeMapping, valueList) {
                var mapping = {};
                mapping.entityAttributeValueMappingId = null;
                mapping.profileId = profileAttributeMapping.profileId;
                mapping.attributeId = profileAttributeMapping.attributeId;
                mapping.attributeValueId = null;
                mapping.profileAttributeMappingId = profileAttributeMapping.profileAttributeMappingId;
                mapping.entityType = "ASSET";
                mapping.entityId = null;
                mapping.creationDate = null;
                mapping.createdBy = appUtil.createGeneratedBy();
                mapping.status = "ACTIVE";
                mapping.valueList = valueList; // temporary property to store list of values associated with particular attribute
                mapping.isMandatory = profileAttributeMapping.mandatoryAtAsset;
                mapping.standAlone = profileAttributeMapping.standAlone;
                mapping.attributeValue = null; // this field will be used in case if profile attribute mapping is standalone
                return mapping;
            }

            $scope.reset = function () {
                closeModal();
            };

            $scope.setSkuImageViaUploadDoc = function (sku) {
                var skuId = sku.skuId;
                $fileUploadService.openFileModal("Upload Sku Image", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if (file.size > 204800) {
                        $toastService.create('File size should not be greater than 2 Mb');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (appUtil.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('skuId', skuId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.productManagement.uploadSkuImage,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != null) {
                                sku.skuImage = response;
                                $scope.skuList.map(function (item) {
                                    if (item.skuId == sku.skuId) {
                                        item = sku;
                                    }
                                });
                                appUtil.updateSkuInCache(sku);
                                $toastService.create("Upload successful");
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            }

        }]);
