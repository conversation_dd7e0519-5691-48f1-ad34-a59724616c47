<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .col.s2 {
        margin-top: 16px;
    }

    .isExclusionEntry{
        color: red ;

    }


</style>
<nav style="position: fixed; top:0;z-index: 9;">
    <div class="row standardView" style="margin-bottom:0px;font-size:16px;">
        <div class="col s4">
            <a href="#" data-activates="nav-mobile" class="button-collapse top-nav full hide-on-large-only">
                <i class="material-icons">menu</i>
            </a> &nbsp;
            <img src="img/logo.png" style="height: 50px; float: left;"/><span class="page-title white-text">SuMo </span>
            <p style="margin: 0 0 0 30px;display: inline;text-transform: uppercase;">{{unitName}}</p>
        </div>
        <div class="col s2" data-ng-show="showUnitLogin">
            <select ui-select2
                    class="form-control"
                    style="width: 100% !important"
                    data-placeholder="Select a Unit"
                    ng-model="outlet"
                    data-ng-change="relogin(outlet)"
                    ng-options="outlet as outlet.name for outlet in userUnitArray">
            </select>
        </div>
        <div class="col s3">
            <div class="right">
                <p style="display: inline;">User: </p>
                <p style="margin:0; margin-left: 0px; display: inline;text-transform: uppercase;">
                    {{userName}}({{userId}})</p>
            </div>
        </div>
        <div class="col s3">

            <span data-ng-if="restrictAll" class="chip red white-text">
                Day Close in Progress
            </span>
            <span data-ng-click="goToNewRegularOrdering(orderingEvents)" data-ng-if="orderingEvents.length > 0" class="chip red white-text clickable">
                Regular Order Pending
            </span>

            <div class="row" style="margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between;">
                <div class="col s8" acl-action="SCM_HOD">
                    <div class="switch">
                        <label>
                            Company Filter
                            <input type="checkbox" id="toggleSwitch" data-ng-model="supScmAdmnFilter" data-ng-change="toggleCompanyFilter()">
                            <span class="lever"></span>
                        </label>
                    </div>
                </div>
                <div class="col s4" style="text-align: right;">
                    <a href="#" data-ng-click="logout()" class="white-text btn"
                       style="margin: 0;">
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row mobileView" style="margin-bottom:0px;font-size:16px;">
        <div class="col s4">
            <a href="#" data-activates="nav-mobile" class="button-collapse top-nav full hide-on-large-only">
                <i class="material-icons">menu</i>
            </a> &nbsp;
            <img src="img/logo.png" style="height: 50px; float: left;"/><span class="page-title white-text">SuMo </span>
            <p style="margin: 0 0 0 30px;display: inline;text-transform: uppercase;">{{unitName}}</p>
            <span class="material-icons" id="dropdown-btn" ng-click="showNavMenu()">
                keyboard_double_arrow_down
            </span>
        </div>
        <div class="mobileViewMenu collapsedMenu">
            <div class="col s2" data-ng-show="showUnitLogin">
                <select ui-select2
                        class="form-control"
                        style="width: 100% !important"
                        data-placeholder="Select a Unit"
                        ng-model="outlet"
                        data-ng-change="relogin(outlet)"
                        ng-options="outlet as outlet.name for outlet in userUnitArray">
                </select>
            </div>
            <div class="col s4">
                <center>
                    <div>
                        <p style="display: inline;">User: </p>
                        <p style="margin:0; margin-left: 0px; display: inline;text-transform: uppercase;">
                            {{userName}}({{userId}})</p>
                    </div>
                </center>
            </div>
            <div class="col">
                <center>
            <span data-ng-if="restrictAll" class="chip red white-text">
                Day Close in Progress
            </span>

            <div class="row" style="margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between;">
                <div class="col s8" acl-action="SCM_HOD">
                    <div class="switch">
                        <label>
                            Company Filter
                            <input type="checkbox" id="toggleSwitch" data-ng-model="supScmAdmnFilter" data-ng-change="toggleCompanyFilter()">
                            <span class="lever"></span>
                            On
                        </label>
                    </div>
                </div>
                <div class="col s4" style="text-align: right;">
                    <a href="#" data-ng-click="logout()" class="white-text btn"
                       style="margin: 0;">
                        Logout
                    </a>
                </div>
            </div>
                </center>
            </div>
        </div>

    </div>
</nav>


<div class="row" data-ng-init="init()">
    <div class="col s12 m4 l3">
        <div id="nav-mobile" class="side-nav fixed" style="top:65px;">
            <ul class="collapsible" id="menu" data-collapsible="accordion" style="transform: translateX(0%);">
                <li>
                    <div class="collapsible-header waves-effect waves-light lighten-5" acl-menu="SPM">Product
                        Management
                    </div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('productList');activeMenu = 'productList'"
                                data-ng-class="{active:activeMenu == 'productList'}">
                                <a ui-sref=".productList" class="waves-effect waves-light" acl-sub-menu="SPMPL">Products
                                    List</a>
                            </li>

                            <li data-ng-click="activateLoader('skuList');activeMenu = 'skuList'" data-ng-class="{active:activeMenu == 'skuList'}">
                                <a ui-sref=".skuList" class="waves-effect waves-light" acl-sub-menu="SPMSL">SKU List</a>
                            </li>
                            <li data-ng-click="activateLoader('addProduct');activeMenu = 'addProduct'"
                                data-ng-class="{active:activeMenu == 'addProduct'}">
                                <a ui-sref=".addProduct" class="waves-effect waves-light" acl-sub-menu="SPMANP">Add New
                                    Product</a>
                            </li>
                            <li data-ng-click="activateLoader('addAttribute');activeMenu = 'addAttribute'"
                                data-ng-class="{active:activeMenu == 'addAttribute'}">
                                <a ui-sref=".addAttribute" class="waves-effect waves-light" acl-sub-menu="SPMPL">Add and
                                    List Attribute</a>
                            </li>
                            <li data-ng-click="activateLoader('addProfile');activeMenu = 'addProfile'"
                                data-ng-class="{active:activeMenu == 'addProfile'}">
                                <a ui-sref=".addProfile" class="waves-effect waves-light" acl-sub-menu="SPMANP">Add
                                    Profile & Mappings</a>
                            </li>
                            <li data-ng-click="activateLoader('addSKU');activeMenu = 'addSKU'" data-ng-class="{active:activeMenu == 'addSKU'}">
                                <a ui-sref=".addSKU" class="waves-effect waves-light" acl-sub-menu="SPMANS">Add New
                                    SKU</a>
                            </li>

                            <li data-ng-click="activateLoader('skuPriceUpdate');activeMenu = 'skuPriceUpdate'"
                                data-ng-class="{active:activeMenu == 'skuPriceUpdate'}">
                                <a ui-sref=".skuPriceUpdate" class="waves-effect waves-light" acl-sub-menu="SPMSPU">SKU
                                    Price Update</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-if="isWHOrKitchen">
                    <div class="collapsible-header waves-effect waves-light">Sumo User Requests</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('userProductCreation');activeMenu = 'userProductCreation'"
                                data-ng-class="{active:activeMenu == 'userProductCreation'}">
                                <a ui-sref=".addProduct({empType:'USER'})" class="waves-effect waves-light" acl-sub-menu="SURNPR">New Product Request</a>
                            </li>
                            <li data-ng-click="activateLoader('productDashboard');activeMenu = 'productDashboard'" data-ng-class="{active:activeMenu == 'productDashboard'}">
                                <a ui-sref=".productDashboard" class="waves-effect waves-light">Product Request Dashboard</a>
                            </li>
                            <li data-ng-click="activateLoader('userSkuCreation');activeMenu = 'userSkuCreation'"
                                data-ng-class="{active:activeMenu == 'userSkuCreation'}">
                                <a ui-sref=".addSKU({empType:'USER'})" class="waves-effect waves-light" acl-sub-menu="SURNSR">New SKU Request</a>
                            </li>
                            <li data-ng-click="activateLoader('skuDashboard');activeMenu = 'skuDashboard'" data-ng-class="{active:activeMenu == 'skuDashboard'}">
                                <a ui-sref=".skuDashboard" class="waves-effect waves-light">SKU Request Dashboard</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorRequest');activeMenu = 'vendorRequest'"
                                data-ng-class="{active:activeMenu == 'vendorRequest'}">
                                <a ui-sref=".vendorRequest({ isOnlyForNewRegistration: true })" class="waves-effect waves-light">Vendor Request</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light lighten-5" acl-menu="SPM,ASTTAGMENU">Asset Management
                    </div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
<!--                            <li data-ng-click="activateLoader('assetRecoveryList');activeMenu = 'assetRecoveryList'"-->
<!--                                data-ng-class="{active:activeMenu == 'assetRecoveryList'}">-->
<!--                                <a ui-sref=".assetRecoveryList" class="waves-effect waves-light" acl-sub-menu="SPMPL">Asset-->
<!--                                    Recovery</a>-->
<!--                            </li>-->
                            <li data-ng-click="activateLoader('fixedAssetRecovery');activeMenu = 'fixedAssetRecovery'"
                                data-ng-class="{active:activeMenu == 'fixedAssetRecovery'}">
                                <a ui-sref=".fixedAssetRecovery" class="waves-effect waves-light" acl-sub-menu="ASTRCVR">Asset Recovery New</a>
                            </li>
                            <li data-ng-click="activateLoader('assetInventoryList');activeMenu = 'assetInventoryList'"
                                data-ng-class="{active:activeMenu == 'assetInventoryList'}">
                                <a ui-sref=".assetInventoryList" class="waves-effect waves-light" acl-sub-menu="SPMPL">Asset
                                    Inventory</a>
                            </li>
                            <li data-ng-click="activateLoader('assetList');activeMenu = 'assetList'"
                                data-ng-class="{active:activeMenu == 'assetList'}">
                                <a ui-sref=".assetList" class="waves-effect waves-light" acl-sub-menu="SPMPL,ASTTAGM">Assets
                                    List</a>
                            </li>
                            <li data-ng-click="activateLoader('assetTransfers');activeMenu = 'assetTransfers'"
                                data-ng-class="{active:activeMenu == 'assetTransfers'}">
                                <a ui-sref=".assetTransfers" class="waves-effect waves-light" acl-sub-menu="ATC">Asset Transfers</a>
                            </li>
                            <li data-ng-click="activateLoader('assetConvertor');activeMenu = 'assetConvertor'"
                                data-ng-class="{active:activeMenu == 'assetConvertor'}" acl-sub-menu="mnktrnsfr">
                                <a ui-sref=".assetConvertor" class="waves-effect waves-light" >Asset Convertor</a>
                            </li>
                            <li data-ng-click="activateLoader('nonScannableMapping');activeMenu = 'nonScannableMapping'"
                                data-ng-if="isWHOrKitchen"
                                data-ng-class="{active:activeMenu == 'nonScannableMapping'}" acl-sub-menu="mnktrnsfr">
                                <a ui-sref=".nonScannableMapping" class="waves-effect waves-light" >Non Scannable Asset Mapping</a>
                            </li>
                            <!--<li data-ng-click="activateLoader();activeMenu = 'assetLostConfirmation'" data-ng-class="{active:activeMenu == 'assetLostConfirmation'}">-->
                            <!--<a ui-sref=".assetLostConfirmation" class="waves-effect waves-light" acl-sub-menu="SPMPL">Asset Lost Confirmation</a>-->
                            <!--</li>-->
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light lighten-5" acl-menu="ASTAPRV" >Approvals
                    </div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('lostTag');activeMenu = 'lostTag'"
                                data-ng-class="{active:activeMenu == 'lostTag'}">
                                <a ui-sref=".lostTag" class="waves-effect waves-light" >Lost Tag</a>
                            </li>
                            <li data-ng-click="activateLoader('lostAsset');activeMenu = 'lostAsset'"
                                data-ng-class="{active:activeMenu == 'lostAsset'}" acl-sub-menu="SCMALA">
                                <a ui-sref=".lostAsset" class="waves-effect waves-light" >Lost Asset</a>
                            </li>
                            <li data-ng-click="activateLoader('foundAsset'); activeMenu = 'foundAsset'"
                                data-ng-class="{active:activeMenu == 'foundAsset'}">
                                <a ui-sref=".foundAsset" class="waves-effect waves-light" >Found Asset</a>
                            </li>

                        </ul>
                    </div>
                </li>
                <li data-ng-show="isProductManager">
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SHMATT">Manage Attributes</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('attrValues');activeMenu = 'attrValues'"
                                data-ng-class="{active:activeMenu == 'attrValues'}">
                                <a ui-sref=".attrValues" class="waves-effect waves-light">Attribute Values</a>
                            </li>
                            <li data-ng-click="activateLoader('mapAttrValues');activeMenu = 'mapAttrValues'"
                                data-ng-class="{active:activeMenu == 'mapAttrValues'}">
                                <a ui-sref=".mapAttrValues" class="waves-effect waves-light">Category-Attribute
                                    Values</a>
                            </li>
                            <li data-ng-click="activateLoader('attrMappings');activeMenu = 'attrMappings'"
                                data-ng-class="{active:activeMenu == 'attrMappings'}">
                                <a ui-sref=".attrMappings" class="waves-effect waves-light">Category Attribute
                                    Mappings</a>
                            </li>
                            <li data-ng-click="activateLoader('attrProductMappings');activeMenu = 'attrProductMappings'"
                                data-ng-class="{active:activeMenu == 'attrProductMappings'}">
                                <a ui-sref=".attrProductMappings" class="waves-effect waves-light">Product Attribute
                                    Mappings</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-show="isProductManager">
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SHMAPA">Manage Packaging
                        Mappings
                    </div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('productPackaging');activeMenu = 'productPackaging'"
                                data-ng-class="{active:activeMenu == 'productPackaging'}">
                                <a ui-sref=".productPackaging" class="waves-effect waves-light">Product Packagings</a>
                            </li>
                            <li data-ng-click="activateLoader('regionProductPackaging');activeMenu = 'regionProductPackaging'"
                                data-ng-class="{active:activeMenu == 'regionProductPackaging'}">
                                <a ui-sref=".regionProductPackaging" class="waves-effect waves-light">Region Product Packaging Mappings</a>
                            </li>
                            <li data-ng-click="activateLoader('mapPackaging');activeMenu = 'mapPackaging'"
                                data-ng-class="{active:activeMenu == 'mapPackaging'}">
                                <a ui-sref=".mapPackaging" class="waves-effect waves-light">Product Mappings</a>
                            </li>
                            <li data-ng-click="activateLoader('mapSkuPackaging');activeMenu = 'mapSkuPackaging'"
                                data-ng-class="{active:activeMenu == 'mapSkuPackaging'}">
                                <a ui-sref=".mapSkuPackaging" class="waves-effect waves-light">SKU Mappings</a>
                            </li>
                            <li data-ng-click="activateLoader('productShelfLife');activeMenu = 'productShelfLife'"
                                data-ng-class="{active:activeMenu == 'productShelfLife'}">
                                <a ui-sref=".productShelfLife" class="waves-effect waves-light">Product Shelf Life Definition</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-show="isProductManager || isVendorManager">
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SVMMSH">Vendor Management</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('vendorManagement');activeMenu = 'vendorManagement'"
                                data-ng-class="{active:activeMenu == 'vendorManagement'}">
                                <a ui-sref=".vendorManagement" class="waves-effect waves-light" acl-sub-menu="SVMVSH">Vendors</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorRequest');activeMenu = 'vendorRequest'"
                                data-ng-class="{active:activeMenu == 'vendorRequest'}">
                                <a ui-sref=".vendorRequest({ isOnlyForNewRegistration: false })" class="waves-effect waves-light" acl-sub-menu="VRMENU">Vendor
                                    Request</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorManagement');activeMenu = 'vendorManagement'"
                                data-ng-class="{active:activeMenu == 'vendorManagement'}">
                                <a ui-sref=".manageVendorDebitBalance" class="waves-effect waves-light">Ledger
                                    Balance</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorManagement');activeMenu = 'vendorManagement'"
                                data-ng-class="{active:activeMenu == 'vendorManagement'}">
                                <a ui-sref=".sendTDSMailToVendor" class="waves-effect waves-light" acl-sub-menu="SVMMTS">Mail
                                    TDS Certificate</a>
                            </li>
                            <li data-ng-click="activateLoader('skuPriceHistory');activeMenu = 'skuPriceHistory'"
                                data-ng-class="{active:activeMenu == 'skuPriceHistory'}">
                                <a ui-sref=".skuPriceHistory" class="waves-effect waves-light" acl-sub-menu="SPMSPU">SKU
                                    Price History</a>
                            </li>
                            <li data-ng-click="activateLoader('skuPriceHistory');activeMenu = 'ldcVendor'"
                                data-ng-class="{active:activeMenu == 'ldcVendor'}">
                                <a ui-sref=".ldcVendor" class="waves-effect waves-light">Ldc Vendor</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-if="!restrictAll" acl-menu="VRECM">
                    <div class="collapsible-header waves-effect waves-light">Vendor Receivings</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('vendorGrCreate');activeMenu = 'vendorGrCreate'"
                                data-ng-class="{active:activeMenu == 'vendorGrCreate'}" acl-sub-menu="VRECCGR">
                                <a ui-sref=".vendorGrCreate" class="waves-effect waves-light">Create GR</a>
                            </li>
                            <li data-ng-click="activateLoader('approveRegularVendorGR');activeMenu = 'approveRegularVendorGR'"
                                data-ng-class="{active:activeMenu == 'approveRegularVendorGR'}" acl-sub-menu="VRECAGR">
                                <a ui-sref=".approveRegularVendorGR({view:true})" class="waves-effect waves-light">GR
                                    Quality Approve</a>
                            </li>
                            <li data-ng-click="activateLoader('viewVendorGR');activeMenu = 'viewVendorGR'"
                                data-ng-class="{active:activeMenu == 'viewVendorGR'}" acl-sub-menu="VRECVGR">
                                <a ui-sref=".viewVendorGR({view:true})" class="waves-effect waves-light">View GR</a>
                            </li>
                            <li data-ng-click="activateLoader('viewRejectedVendorGR');activeMenu = 'viewRejectedVendorGR'"
                                data-ng-class="{active:activeMenu == 'viewRejectedVendorGR'}">
                                <a ui-sref=".viewRejectedVendorGR({view:true})" class="waves-effect waves-light">Rejected
                                    QC GR</a>
                            </li>
                            <li data-ng-click="activateLoader('viewVendorPRtoGR');activeMenu = 'viewVendorPRtoGR'"
                                data-ng-class="{active:activeMenu == 'viewVendorPRtoGR'}">
                                <a ui-sref=".viewVendorPRtoGR({view:true})" class="waves-effect waves-light">View PO to
                                    GR</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li acl-menu="VOM">
                    <div class="collapsible-header waves-effect waves-light">Vendor Ordering</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-if="canCreatePO" data-ng-click="activateLoader('vendorOrderCreate');activeMenu = 'vendorOrderCreate'"
                                data-ng-class="{active:activeMenu == 'vendorOrderCreate'}" acl-sub-menu="VOMRPO">
                                <a ui-sref=".vendorOrderCreate" class="waves-effect waves-light">Raise PO</a>
                            </li>
                            <li data-ng-click="activateLoader('approvePo');activeMenu = 'approvePo'"
                                data-ng-if="hasApprovePermissions && isWHOrKitchen"
                                data-ng-class="{active:activeMenu == 'approvePo'}" acl-sub-menu="VOMAPO">
                                <a ui-sref=".approvePo({viewPO:false})" class="waves-effect waves-light">Approve PO</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('viewPO');activeMenu = 'viewPO'"
                                data-ng-class="{active:activeMenu == 'viewPO'}" acl-sub-menu="VOMVPO">
                                <a ui-sref=".approvePo({viewPO:true})" class="waves-effect waves-light">View PO</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- B2B Sales and Invoicing Section -->
                <li data-ng-if="isWHOrKitchen && !restrictAll" acl-menu="VIM">
                    <div class="collapsible-header waves-effect waves-light">Vendor Invoicing</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('vendorInvoiceCreate');activeMenu = 'vendorInvoiceCreate'"
                                data-ng-class="{active:activeMenu == 'vendorInvoiceCreate'}" acl-sub-menu="VIMRINV">
                                <a ui-sref=".vendorInvoiceCreate({returnInvoice:false})" class="waves-effect waves-light">Raise Invoice
                                    Request</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorInvoiceCreate');activeMenu = 'vendorInvoiceCreate'"
                                data-ng-class="{active:activeMenu == 'vendorInvoiceCreate'}" acl-sub-menu="VIMRINV">
                                <a ui-sref=".vendorInvoiceCreate({returnInvoice:true})" class="waves-effect waves-light">Raise Return Invoice
                                    Request</a>
                            </li>
                            <li data-ng-click="activateLoader('approveInvoice');activeMenu = 'approveInvoice'"
                                data-ng-class="{active:activeMenu == 'approveInvoice'}" acl-sub-menu="VIMAINV">
                                <a ui-sref=".approveInvoice({viewInvoice:false,raiseCreditNote:false})" class="waves-effect waves-light">Approve
                                    Invoice Request</a>
                            </li>
                            <li data-ng-click="activateLoader('viewInvoice');activeMenu = 'viewInvoice'"
                                data-ng-class="{active:activeMenu == 'viewInvoice'}" acl-sub-menu="VIMVINV">
                                <a ui-sref=".approveInvoice({viewInvoice:true,raiseCreditNote:false})" class="waves-effect waves-light">View
                                    Invoice Request</a>
                            </li>
                            <li data-ng-click="activateLoader('raiseCreditNote');activeMenu = 'raiseCreditNote'"
                                data-ng-class="{active:activeMenu == 'viewInvoice'}" acl-sub-menu="VIMVINV">
                                <a ui-sref=".approveInvoice({raiseCreditNote:true})" class="waves-effect waves-light">Raise Credit Note</a>
                            </li>
                            <li data-ng-click="activateLoader('viewCreditNote');activeMenu = 'viewCreditNote'"
                                data-ng-class="{active:activeMenu == 'viewCreditNote'}" acl-sub-menu="VIMVINV">
                                <a ui-sref=".viewCreditNote" class="waves-effect waves-light">View Credit Note</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-show="isWHOrKitchen" acl-menu="VCM">
                    <div class="collapsible-header waves-effect waves-light">Vendor Price Management</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('vendorToSkuPriceMapping');activeMenu = 'vendorToSkuPriceMapping'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuPriceMapping'}">
                                <a ui-sref=".vendorToSkuPriceMapping({isCafe:false})" class="waves-effect waves-light">Vendor to SKU Price Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorToSkuPriceApproval');activeMenu = 'vendorToSkuPriceApproval'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuPriceApproval'}" acl-sub-menu="VCMRP">
                                <a ui-sref=".vendorToSkuPriceApproval({isCafe:false})" class="waves-effect waves-light">Vendor to SKU Price Approval</a>
                            </li>
                            <!-- <li data-ng-click="activateLoader('vendorToSkuPrice');activeMenu = 'vendorToSkuPrice'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuPrice'}" acl-sub-menu="VCMSP">
                                <a ui-sref=".vendorToSkuPrice({isCafe:false})" class="waves-effect waves-light">Vendor to SKU Price</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorToSkuRequest');activeMenu = 'vendorToSkuRequest'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuRequest'}" acl-sub-menu="VCMRR">
                                <a ui-sref=".vendorToSkuRequest({isCafe:false})" class="waves-effect waves-light">Raised Vendor SKU Price Request</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorToSkuPreview');activeMenu = 'vendorToSkuPreview'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuPreview'}" acl-sub-menu="VCMRP">
                                <a ui-sref=".vendorToSkuPreview({isCafe:false})" class="waves-effect waves-light">Raised Vendor SKU Price Preview</a>
                            </li> -->
                            <li data-ng-click="activateLoader('vendorContract');activeMenu = 'vendorContract'"
                                data-ng-class="{active:activeMenu == 'vendorContract'}" acl-sub-menu="VCMVC">
                                <a ui-sref=".vendorContract({isCafe:false})" class="waves-effect waves-light">View Contracts</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li data-ng-if="!restrictAll" acl-menu="ORDM">
                    <div class="collapsible-header waves-effect waves-light">Ordering</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('refOrderCreate');activeMenu = 'refOrderCreate'" data-ng-hide="true"
                                data-ng-class="{active:activeMenu == 'refOrderCreate'}" acl-sub-menu="ORDRO">
                                <a ui-sref=".refOrderCreate" class="waves-effect waves-light">Regular Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('refOrderCreateV2');activeMenu = 'refOrderCreateV2'" data-ng-hide="true"
                                data-ng-class="{active:activeMenu == 'refOrderCreateV2'}" data-ng-if="userId == 140199 || userId == 126458 || userId == 120063">
                                <a ui-sref=".refOrderCreateV2" class="waves-effect waves-light">New Regular Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('refOrderCreateV1');activeMenu = 'refOrderCreateV1'" data-ng-hide="true"
                                data-ng-class="{active:activeMenu == 'refOrderCreateV1'}" data-ng-if="userId == 140199 || userId == 126458 || userId == 120063">
                                <a ui-sref=".refOrderCreateV1" class="waves-effect waves-light">Updated Regular Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('suggestiveOrdering');activeMenu = 'suggestiveOrdering'"
                                data-ng-class="{active:activeMenu == 'suggestiveOrdering'}">
                                <a ui-sref=".suggestiveOrdering" class="waves-effect waves-light">Suggestive Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('adhocOrderCreate');activeMenu = 'adhocOrderCreate'"
                                data-ng-class="{active:activeMenu == 'adhocOrderCreate'}" acl-sub-menu="ORDAO">
                                <a ui-sref=".adhocOrderCreate({assetOrder:false})" class="waves-effect waves-light">Ad-hoc
                                    Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('assetOrderCreate');activeMenu = 'assetOrderCreate'"
                                data-ng-class="{active:activeMenu == 'assetOrderCreate'}" acl-sub-menu="ORDASO">
                                <a ui-sref=".adhocOrderCreate({assetOrder:true})" class="waves-effect waves-light">Asset
                                    Ordering</a>
                            </li>
                            <li data-ng-if="!isWHOrKitchen" data-ng-click="activateLoader('specialOrderCreate');activeMenu = 'specialOrderCreate'"
                                data-ng-class="{active:activeMenu == 'specialOrderCreate'}" acl-sub-menu="ORDSO">
                                <a ui-sref=".specialOrderCreate" class="waves-effect waves-light">Specialized
                                    Ordering</a>
                            </li>
                            <li data-ng-click="activateLoader('orderingSchedule');activeMenu = 'orderingSchedule'"
                                data-ng-class="{active:activeMenu == 'orderingSchedule'}" data-ng-if="userId == 140199 || userId == 126458 || userId == 120063">
                                <a ui-sref=".orderingSchedule" class="waves-effect waves-light">Ordering Schedule</a>
                            </li>
                            <li data-ng-click="activateLoader('allOrderingSchedules');activeMenu = 'allOrderingSchedules'"
                                data-ng-class="{active:activeMenu == 'allOrderingSchedules'}" data-ng-if="userId == 140199 || userId == 126458 || userId == 120063">
                                <a ui-sref=".allOrderingSchedules" class="waves-effect waves-light">All Delivery Schedules</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-if="!restrictAll" acl-menu="TRNM">
                    <div class="collapsible-header waves-effect waves-light">Transfers</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('trOrderCreate');activeMenu = 'trOrderCreate'"
                                data-ng-class="{active:activeMenu == 'trOrderCreate'}" acl-sub-menu="TRNCT">
                                <a ui-sref=".trOrderCreate" class="waves-effect waves-light">Create Transfer</a>
                            </li>
                            <li data-ng-hide="isWHOrKitchen" data-ng-click="activateLoader('trGrOrderCreate');activeMenu = 'trGrOrderCreate'"
                                data-ng-class="{active:activeMenu == 'trGrOrderCreate'}" acl-sub-menu="TRNCT">
                                <a ui-sref=".trGrOrderCreate" class="waves-effect waves-light">Create Specialized Transfer And GR</a>
                            </li>
                            <li  data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('trOrderCreateBulk');activeMenu = 'trOrderCreateBulk'"
                                 data-ng-class="{active:activeMenu == 'trOrderCreateBulk'}" acl-sub-menu="TRNCT">
                                <a ui-sref=".trOrderCreateBulk" class="waves-effect waves-light">Create Bulk Transfer</a>
                            </li>
                            <li data-ng-click="activateLoader('standaloneTO');activeMenu = 'standaloneTO'"
                                data-ng-class="{active:activeMenu == 'standaloneTO'}" acl-sub-menu="TRNST">
                                <a ui-sref=".standaloneTO" class="waves-effect waves-light">Standalone Transfer</a>
                            </li>
                            <li data-ng-click="activateLoader('standaloneTONew');activeMenu = 'standaloneTONew'"
                                data-ng-class="{active:activeMenu == 'standaloneTONew'}" acl-sub-menu="TRNST">
                                <a ui-sref=".standaloneTONew" class="waves-effect waves-light">Fixed Asset Standalone Transfer</a>
                            </li>

                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('bulkStandaloneTO');activeMenu = 'bulkStandaloneTO'"
                                data-ng-class="{active:activeMenu == 'bulkStandaloneTO'}" acl-sub-menu="bst">
                                <a ui-sref=".bulkStandaloneTO" class="waves-effect waves-light">Bulk Standalone Transfer</a>
                            </li>
                            <!-- <li data-ng-click="activateLoader('standaloneAssetTO');activeMenu = 'standaloneAssetTO'"
                                data-ng-class="{active:activeMenu == 'standaloneAssetTO'}" acl-sub-menu="TRNST">
                                <a ui-sref=".standaloneAssetTO" class="waves-effect waves-light">Standalone Asset
                                    Transfer</a>
                            </li> -->
                            <li data-ng-if="!isWHOrKitchen" data-ng-click="activateLoader('monkIngredientsConverter');activeMenu = 'monkIngredientsConverter'"
                                data-ng-class="{active:activeMenu == 'monkIngredientsConverter'}" acl-sub-menu="mnktrnsfr">
                                <a ui-sref=".monkIngredientsConverter" class="waves-effect waves-light">Monk Ingredients Converter</a>
                            </li>

                            <!--<li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader();activeMenu = 'trEpCreate'" data-ng-class="{active:activeMenu == 'trEpCreate'}" acl-sub-menu="TRNTE">
                                <a ui-sref=".trEpCreate" class="waves-effect waves-light">Transfer to Vendor</a>
                            </li>-->
                            <li data-ng-if="!isWHOrKitchen" data-ng-click="activateLoader('acknowledgeRO');activeMenu = 'acknowledgeRO'"
                                data-ng-class="{active:activeMenu == 'acknowledgeRO'}" acl-sub-menu="TRNAO">
                                <a ui-sref=".acknowledgeRO" class="waves-effect waves-light">Acknowledge Orders</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('prodPlanning');activeMenu = 'prodPlanning'"
                                data-ng-class="{active:activeMenu == 'prodPlanning'}" acl-sub-menu="TRNPP">
                                <a ui-sref=".prodPlanning" class="waves-effect waves-light">Production Planning</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('prodHistory');activeMenu = 'prodHistory'"
                                data-ng-class="{active:activeMenu == 'prodHistory'}" acl-sub-menu="TRNPH">
                                <a ui-sref=".prodHistory" class="waves-effect waves-light">Production History</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('prodBooking');activeMenu = 'prodBooking'"
                                data-ng-class="{active:activeMenu == 'prodBooking'}" acl-sub-menu="RECPB">
                                <a ui-sref=".prodBooking" class="waves-effect waves-light">Production Booking</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('viewRecipe');activeMenu = 'viewRecipe'"
                                data-ng-class="{active:activeMenu == 'viewRecipe'}" acl-sub-menu="RECVR">
                                <a ui-sref=".viewRecipe" class="waves-effect waves-light">View Recipe</a>
                            </li>
                            <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('bookingHistory');activeMenu = 'bookingHistory'"
                                data-ng-class="{active:activeMenu == 'bookingHistory'}" acl-sub-menu="TRNBH">
                                <a ui-sref=".bookingHistory" class="waves-effect waves-light">Booking History</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li data-ng-if="isWHOrKitchen && !restrictAll" acl-menu="GTPSMG">
                    <div class="collapsible-header waves-effect waves-light">Gatepass Management</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('createGatepass');activeMenu = 'createGatepass'"
                                data-ng-class="{active:activeMenu == 'createGatepass'}" acl-sub-menu="CRGTPS">
                                <a data-ui-sref=".createGatepass" class="waves-effect waves-light">Create Gatepass</a>
                            </li>
                            <li data-ng-click="activateLoader('searchGatepass');activeMenu = 'searchGatepass'"
                                data-ng-class="{active:activeMenu == 'searchGatepass'}" acl-sub-menu="SEGTPS">
                                <a data-ui-sref=".searchGatepass" class="waves-effect waves-light">Search Gatepass</a>
                            </li>
                            <li data-ng-click="activateLoader('gatepassVendorMapping');activeMenu = 'gatepassVendorMapping'"
                                data-ng-class="{active:activeMenu == 'gatepassVendorMapping'}" acl-sub-menu="MGGTPS">
                                <a data-ui-sref=".gatepassVendorMapping" class="waves-effect waves-light">Gatepass
                                    Vendor Mapping</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li data-ng-if="!restrictAll" acl-menu="EWDISMAN">
                    <div class="collapsible-header waves-effect waves-light">Dispatch Management</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('createDispatch');activeMenu = 'createDispatch'"
                                data-ng-class="{active:activeMenu == 'createDispatch'}" acl-sub-menu="CREWDIS">
                                <a ui-sref=".createDispatch" class="waves-effect waves-light">Create Dispatch</a>
                            </li>
                            <li data-ng-click="activateLoader('searchDispatch');activeMenu = 'searchDispatch'"
                                data-ng-class="{active:activeMenu == 'searchDispatch'}" acl-sub-menu="SREWDIS">
                                <a ui-sref=".searchDispatch" class="waves-effect waves-light">Search Dispatch</a>
                            </li>
                            <li data-ng-click="activateLoader('vehicleMaster');activeMenu = 'vehicleMaster'"
                                data-ng-class="{active:activeMenu == 'vehicleMaster'}" acl-sub-menu="CREWDIS">
                                <a ui-sref=".vehicleMaster" class="waves-effect waves-light">Vehicle Master</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-if="!restrictAll" acl-menu="RECM">
                    <div class="collapsible-header waves-effect waves-light">Receiving</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('grActivity');activeMenu = 'grActivity'"
                                data-ng-class="{active:activeMenu == 'grActivity'}" acl-sub-menu="RECPR">
                                <a ui-sref=".grActivity" class="waves-effect waves-light">Pending Receiving</a>
                            </li>
                            <li data-ng-click="activateLoader('milkBreadBypass');activeMenu = 'milkBreadBypass'"
                                data-ng-class="{active:activeMenu == 'milkBreadBypass'}" acl-sub-menu="MBBP">
                                <a ui-sref=".milkBreadBypass" class="waves-effect waves-light">Milk Bread Bypass</a>
                            </li>
                            <!--<li data-ng-click="activateLoader();activeMenu = 'standaloneGR'" data-ng-class="{active:activeMenu == 'standaloneGR'}">
                                <a ui-sref=".standaloneGR" class="waves-effect waves-light">Standalone Receiving</a>
                            </li>-->
                        </ul>
                    </div>
                </li>
                <li data-ng-if="!restrictAll" acl-menu="MTM">
                    <div class="collapsible-header waves-effect waves-light">Manage Transactions</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('refOrderFind');activeMenu = 'refOrderFind'"
                                data-ng-class="{active:activeMenu == 'refOrderFind'}" acl-sub-menu="MTRFOM">
                                <a ui-sref=".refOrderFind" class="waves-effect waves-light">Reference Order
                                    Management</a>
                            </li>
                            <li data-ng-click="activateLoader('reqOrderMgt');activeMenu = 'reqOrderMgt'"
                                data-ng-class="{active:activeMenu == 'reqOrderMgt'}" acl-sub-menu="MTRQOM">
                                <a ui-sref=".reqOrderMgt" class="waves-effect waves-light">Request Order Management</a>
                            </li>
                            <li data-ng-if= "isWHOrKitchen" data-ng-click="activateLoader('BulkTrOrderMgt');activeMenu = 'BulkTrOrderMgt'"
                                data-ng-class="{active:activeMenu == 'BulkTrOrderMgt'}" acl-sub-menu="MTTOM">
                                <a ui-sref=".BulkTrOrderMgt" class="waves-effect waves-light">Bulk Transfer Order Management</a>
                            </li>
                            <li  data-ng-click="activateLoader('trOrderMgt');activeMenu = 'trOrderMgt'"
                                 data-ng-class="{active:activeMenu == 'trOrderMgt'}" acl-sub-menu="MTTOM">
                                <a ui-sref=".trOrderMgt" class="waves-effect waves-light">Transfer Order Management</a>
                            </li>
                            <li data-ng-click="activateLoader('grOrderMgt');activeMenu = 'grOrderMgt'"
                                data-ng-class="{active:activeMenu == 'grOrderMgt'}" acl-sub-menu="MTGRM">
                                <a ui-sref=".grOrderMgt" class="waves-effect waves-light">Goods Received Management</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li data-ng-if="!isWHOrKitchen" acl-menu="UIM">
                    <div class="collapsible-header waves-effect waves-light">Update</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('updateInventory');activeMenu = 'updateInventory'"
                                data-ng-class="{active:activeMenu == 'updateInventory'}" acl-menu="UIM">
                                <a ui-sref=".updateInventory" class="waves-effect waves-light">Update Inventory</a>
                            </li>
                            <li data-ng-click="activateLoader('varianceEdit');activeMenu = 'varianceEdit'"
                                data-ng-class="{active:activeMenu == 'varianceEdit'}" acl-menu="UIM">
                                <a ui-sref=".varianceEdit" class="waves-effect waves-light">Variance Edit</a>
                            </li>
                            <li data-ng-click="activateLoader('stockTake');activeMenu = 'stockTake'"
                                data-ng-class="{active:activeMenu == 'stockTake'}" acl-sub-menu="SECU">
                                <a ui-sref=".stockTake" class="waves-effect waves-light">Update Stock Calendar</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li data-ng-click="activateLoader('acknowledgeVariance');activeMenu = 'acknowledgeVariance'"
                    data-ng-class="{active:activeMenu == 'acknowledgeVariance'}">
                    <a ui-sref=".acknowledgeVariance" class="waves-effect waves-light">Acknowledge Variance</a>
                </li>


                <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('currentInventory');activeMenu = 'currentInventory'"
                    data-ng-class="{active:activeMenu == 'currentInventory'}" acl-menu="CIM">
                    <a ui-sref=".currentInventory" class="waves-effect waves-light">Current Inventory</a>
                    <!--acl-menu="CIM"-->
                </li>
                <li data-ng-click="activateLoader('currentPrice');activeMenu = 'currentPrice'" data-ng-class="{active:activeMenu == 'currentPrice'}"
                    acl-menu="CPR">
                    <a ui-sref=".currentPrice" class="waves-effect waves-light">Current Price</a>
                </li>

                <li data-ng-if="isWHOrKitchen" data-ng-click="activateLoader('startDayClose');activeMenu = 'startDayClose'"
                    data-ng-class="{active:activeMenu == 'startDayClose'}" acl-menu="DCM">
                    <a ui-sref=".dayClose" class="waves-effect waves-light">Day Close</a>
                <li data-ng-if="!isWHOrKitchen" data-ng-click="activateLoader('wastage');activeMenu = 'wastage'"
                    data-ng-class="{active:activeMenu == 'wastage'}" acl-menu="AWM">
                    <a ui-sref=".wastage" class="waves-effect waves-light">Add Wastage</a>
                </li>
                <!--&lt;!&ndash;</li>&ndash;&gt;-->
                <li data-ng-if="isWHOrKitchen && !restrictAll" data-ng-click="activateLoader('wastage');activeMenu = 'wastage'"
                    data-ng-class="{active:activeMenu == 'wastage'}" acl-menu="AWM">
                    <a ui-sref=".whWastage" class="waves-effect waves-light">Add Wastage</a>
                </li>

                <li data-ng-show="hasReportPrivilege" acl-menu="ARM">
                    <div class="collapsible-header waves-effect waves-light">Admin Reports</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('specializedOrderReport');activeMenu = 'specializedOrderReport'"
                                data-ng-class="{active:activeMenu == 'specializedOrderReport'}" acl-sub-menu="ARSOR">
                                <a ui-sref=".specializedOrderReport" class="waves-effect waves-light">Specialized
                                    Ordering Report</a>
                            </li>
                            <li data-ng-click="activateLoader('CustomSumoReports');activeMenu = 'CustomSumoReports'"
                                data-ng-class="{active:activeMenu == 'CustomSumoReports'}" acl-sub-menu="CSR">
                                <a ui-sref=".CustomSumoReports" class="waves-effect waves-light">Custom Reports</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li data-ng-show="hasReportPrivilege">
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SMMMSH">Manage Mappings</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader('unitToSkuMapping');activeMenu = 'unitToSkuMapping'"
                                data-ng-class="{active:activeMenu == 'unitToSkuMapping'}">
                                <a ui-sref=".unitToSkuMapping({isCafe:false})" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Unit to SKU Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('unitToDerivedProductsMapping');activeMenu = 'unitToDerivedProductsMapping'"
                                data-ng-class="{active:activeMenu == 'unitToDerivedProductsMapping'}">
                                <a ui-sref=".unitToDerivedProductsMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Unit to Derived Products Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('unitToProductProfileMapping');activeMenu = 'unitToProductProfileMapping'"
                                data-ng-class="{active:activeMenu == 'unitToProductProfileMapping'}">
                                <a ui-sref=".unitToProductProfileMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMMVSSH">Unit to Product Profile Mapping</a>
                            </li>
                            <!--<li data-ng-click="activateLoader();activeMenu = 'cafeToSkuMapping'" data-ng-class="{active:activeMenu == 'cafeToSkuMapping'}">
                                <a ui-sref=".unitToSkuMapping({isCafe:true})" class="waves-effect waves-light" acl-sub-menu="SMMCSSH">Cafe to SKU Mapping</a>
                            </li>-->
                            <li data-ng-click="activateLoader('vendorToSkuMapping');activeMenu = 'vendorToSkuMapping'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuMapping'}">
                                <a ui-sref=".vendorToSkuMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMMVSSH">Vendor to SKU Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorToUnitToSkuMapping');activeMenu = 'vendorToUnitToSkuMapping'"
                                data-ng-class="{active:activeMenu == 'vendorToUnitToSkuMapping'}">
                                <a ui-sref=".vendorToUnitToSkuMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMVUSSH">Vendor to Unit to SKU Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('vendorToSkuPriceUpdate');activeMenu = 'vendorToSkuPriceUpdate'"
                                data-ng-class="{active:activeMenu == 'vendorToSkuPriceUpdate'}">
                                <a ui-sref=".vendorToSkuPriceUpdate" class="waves-effect waves-light"
                                   acl-sub-menu="SMVUSPU">Vendor to SKU Price Update</a>
                            </li>
                            <li data-ng-click="activateLoader('businessToCustomerMapping');activeMenu = 'businessToCustomerMapping'"
                                data-ng-class="{active:activeMenu == 'businessToCustomerMapping'}">
                                <a ui-sref=".businessToCustomerMapping" class="waves-effect waves-light"
                                >Business To Customer Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('unitVendorMapping');activeMenu = 'unitVendorMapping'"
                                data-ng-class="{active:activeMenu == 'unitVendorMapping'}">
                                <a ui-sref=".unitVendorMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMUVMCSH">Unit Vendor Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('unitDistanceMapping');activeMenu = 'unitDistanceMapping'"
                                data-ng-class="{active:activeMenu == 'unitDistanceMapping'}">
                                <a ui-sref=".unitDistanceMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Unit Distance Mapping</a>
                            </li>
                            <li data-ng-click="activateLoader('skuPackagingTaxMapping');activeMenu = 'skuPackagingTaxMapping'"
                                data-ng-class="{active:activeMenu == 'skuPackagingTaxMapping'}">
                                <a ui-sref=".skuPackagingTaxMapping" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Sku Packaging Tax Mapping</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <!--classification list for GOODS-->
                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SMMMSH">Classification List</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createClassification')" ui-sref=".createClassification" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Classification</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('subClassification')" ui-sref=".subCreateClassification" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Sub Classification</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('subSubClassification')" ui-sref=".subSubCreateClassification" class="waves-effect waves-light"
                                   acl-sub-menu="SMMUSSH">Sub Sub Classification</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="MNP">Manage Payments</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createPaymentRequest')" ui-sref=".createPaymentRequest" class="waves-effect waves-light"
                                   acl-sub-menu="PRCRT">Create Payment Request</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('raiseSerivePR')" ui-sref=".raiseServicePR" class="waves-effect waves-light" acl-sub-menu="RSRPR">Raise
                                    Payment Request</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('searchPaymentRequest')" ui-sref=".searchPaymentRequest" class="waves-effect waves-light"
                                   acl-sub-menu="PRSRC">Search Payment Request</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('processPaymentRequest')" ui-sref=".processPaymentRequest" class="waves-effect waves-light"
                                   acl-sub-menu="PRSTLP">Process Payment Request</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('vendorAdvancePayment')" ui-sref=".vendorAdvancePayment" class="waves-effect waves-light"
                                   acl-sub-menu="PRSRC">Advance Payments</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('searchDebitNote')" ui-sref=".searchDebitNote" class="waves-effect waves-light">Search Debit Note</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('settlePaymentRequests')" ui-sref=".settlePaymentRequests" class="waves-effect waves-light"
                                   acl-sub-menu="PRSTLP">Settle Payments</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('rejectPaymentRequests')" ui-sref=".rejectPaymentRequests" class="waves-effect waves-light"
                                   acl-sub-menu="PRSTLP">Rejected Payments</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('holidayCalendar')" ui-sref=".holidayCalendar" class="waves-effect waves-light"
                                   acl-sub-menu="PRSTLP">Holiday Calendar</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('specializedOrderInvoice')" ui-sref=".specializedOrderInvoice" class="waves-effect waves-light"
                                   acl-sub-menu="PRCRT">Specialized Order Invoice</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SPP">Projection Calculator</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('productProjections')" ui-sref=".productProjections" class="waves-effect waves-light">
                                    Product Projections </a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('uploadUnitsProjections')" ui-sref=".uploadUnitsProjections" class="waves-effect waves-light">
                                    Upload Units Projections </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light"  acl-menu="SSG">
                        Security Guard</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('b2bOutWardRegister')" ui-sref=".b2bOutWardRegister" class="waves-effect waves-light">
                                    B2B OutWard Register</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('ecomOutWardRegister')" ui-sref=".ecomOutWardRegister" class="waves-effect waves-light">
                                    ECOM OutWard Register</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SOM">Service Orders</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <!-- <li>
                                <a ui-sref=".createSO" class="waves-effect waves-light" acl-sub-menu="CRESO">Create Service Order</a>
                            </li> -->
                            <li>
                                <a data-ng-click="activateLoader('createNewSO')" ui-sref=".createNewSO" class="waves-effect waves-light" acl-sub-menu="CRESO">Create
                                    New Service Order</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('viewSO')" ui-sref=".viewSO({viewSO:true})" class="waves-effect waves-light"
                                   acl-sub-menu="VWSO">Search Service Orders</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('viewSOFalse')" ui-sref=".approveSO({viewSO:false})" class="waves-effect waves-light"
                                   acl-sub-menu="APRSO">Approve Service Orders</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SRM">Service Receiving</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createSR')" ui-sref=".createSR" class="waves-effect waves-light" acl-sub-menu="CRESR">Create
                                    Receiving</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('viewSR')" ui-sref=".viewSR" class="waves-effect waves-light" acl-sub-menu="VSRCV">Search
                                    Receiving</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('viewSoToSr')"  ui-sref=".viewSoToSr" class="waves-effect waves-light">Search
                                    So To Sr</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SRMNP">Service Payments</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createServicePR')" ui-sref=".createServicePR" class="waves-effect waves-light"
                                   acl-sub-menu="RSRPR">Raise Payment Request</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('searchPaymentRequest')" ui-sref=".searchPaymentRequest" class="waves-effect waves-light"
                                   acl-sub-menu="SOSPR">Search Payment Request</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="SRMNP">Classification List</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createClassification')" ui-sref=".createClassification" class="waves-effect waves-light"
                                   acl-sub-menu="RSRPR">Classification</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('subCreateClassification')" ui-sref=".subCreateClassification" class="waves-effect waves-light"
                                   acl-sub-menu="RSRPR">Sub Classification</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('subSubCreateClassification')" ui-sref=".subSubCreateClassification" class="waves-effect waves-light"
                                   acl-sub-menu="RSRPR">Sub Sub Classification</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <!-- additional master document and its mapping-->
                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu ="SMD">Master Documents</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('addMasterDocument')" ui-sref=".addMasterDocument" class="waves-effect waves-light">
                                    Add Master Document
                                </a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('costElementToDocumentMap')" ui-sref=".costElementToDocumentMap" class="waves-effect waves-light">
                                    Cost Element to Document Mapping
                                </a>
                            </li>
                        </ul>
                    </div>

                </li>

                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="CCMAP">Manage Mapping</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('createCostCenter')" ui-sref=".createCostCenter" class="waves-effect waves-light" acl-sub-menu="CCC">Create
                                    Cost Center</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('createCostElement')" ui-sref=".createCostElement" class="waves-effect waves-light" acl-sub-menu="CCE">Create
                                    Cost Element</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('searchCostElement')" ui-sref=".searchCostElement" class="waves-effect waves-light" acl-sub-menu="CCE">Search
                                    Cost Element</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('costCenterToUserMap')" ui-sref=".costCenterToUserMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">User Cost Center Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('employeeBccMapping')" ui-sref=".employeeBccMapping" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">BCC Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('costElementToVendorMap')" ui-sref=".costElementToVendorMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">Cost Element to Vendor Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('vendorToCostElementMap')" ui-sref=".vendorToCostElementMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">Vendor to Cost Element Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('costElementToCostCentreMap')" ui-sref=".costElementToCostCentreMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">Cost Element to Cost Center Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('costCenterToCostElementMap')" ui-sref=".costCenterToCostElementMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">Cost Center to Cost Element Mapping</a>
                            </li>
                            <li>
                                <a data-ng-click="activateLoader('VendorToCostCenterToCostElementMap')" ui-sref=".vendorToCostCentreToCostElementMap" class="waves-effect waves-light"
                                   acl-sub-menu="UCCMAP">Vendor To Cost Center To Cost Element Mapping</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <div class="collapsible-header waves-effect waves-light" acl-menu="CAPMOD">Capex Module</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li>
                                <a data-ng-click="activateLoader('uploadCapex')" ui-sref=".uploadCapex" class="waves-effect waves-light"
                                   acl-sub-menu="RSRPR">Upload Capex</a>
                                <a data-ng-click="activateLoader('viewCapexPoSo')" ui-sref=".viewCapexPoSo" class="waves-effect waves-light">View Capex PO/SO</a>
                            </li>
                        </ul>
                    </div>
                </li>
                <!--<li>
                    <div class="collapsible-header waves-effect waves-light">Support Team</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li data-ng-click="activateLoader();activeMenu = 'supportLink'"
                                data-ng-class="{active:activeMenu == 'supportLink'}">
                                <a ui-sref=".supportLink" class="waves-effect waves-light">Support</a>
                            </li>
                        </ul>
                    </div>
                </li>-->
            </ul>
        </div>
    </div>
</div>
<div id="content" class="col s12 m12 l12" ui-view autoscroll="true"></div>


<!-- View Detail Modal -->
<script type="text/ng-template" id="viewSRDetail.html">
    <div class="row" data-ng-init="initViewModal()">
        <label style="text-align-last: center;">SR# {{sr.id}} issued for {{sr.vendor.name}} {{sr.location.name}}</label>
        <div class="respTable standardView">
            <table class="bordered striped row">
                <thead>
                <tr>
                    <th>Cost Center</th>
                    <th>Cost Element</th>
                    <th>Service Description</th>
                    <th>Unit Price</th>
                    <th>Quantity</th>
                    <th>Tax Rate</th>
                    <th>Cost</th>
                    <th>Tax</th>
                    <th data-ng-show="sr.type == 'PROVISIONAL' ">Drill Down</th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat-start= "item in sr.serviceReceiveItems">
                    <td>{{item.businessCostCenterName}}</td>
                    <td>{{item.costElementName}}[{{item.ascCode}}]</td>
                    <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                    <td>{{item.unitPrice.toFixed(2)}}</td>
                    <td>{{item.receivedQuantity}}</td>
                    <td>{{item.taxRate.toFixed(2)}}</td>
                    <td>{{item.totalCost.toFixed(2)}}</td>
                    <td>{{item.totalTax.toFixed(2)}}</td>
                    <td data-ng-show="item.serviceReceivedItemDrillDown.length > 0"><button class="btn btn-medium "
                                                                                            data-ng-style="isSelectedItem(item) ? {'background-color' : '#85BF3F' }
                         : {}" data-ng-click="showDrillDowns(item)">{{item.expanded ? "Hide Measurements" : "Show Measurements"}}</button></td>
                </tr>
                <tr ng-if="item.expanded" ng-repeat-end="" >
                    <td colspan="10">
                        <table class="row white z-depth-3" >
                            <b class="col s-12 align-center">Measurement Book Details For UOM <span style="color : #85BF3F"> {{item.unitOfMeasure}} </span>:</b>
                            <thead>
                            <tr>
                                <th>Description</th>
                                <th>Received Quantity</th>
                                <!--<th>UOM</th>-->
                                <th>NOS</th>
                                <th>Multiplier</th>
                                <th>Height</th>
                                <th>Length</th>
                                <th>width</th>
                                <th>Cost</th>
                                <th>Tax</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="drilldown in item.serviceReceivedItemDrillDown" data-ng-class="{'isExclusionEntry' :(drilldown.isExclusionEntry == 'Y')}">
                                <td style="word-break: break-all;">{{drilldown.description}}</td>
                                <td>{{drilldown.receivedQuantity}}</td>
                                <!--<td >{{drilldown.sourceUom}}</td>-->
                                <td>{{drilldown.nos}}</td>
                                <td>{{drilldown.multiplier}}</td>
                                <td>{{drilldown.height.toFixed(4)}}</td>
                                <td>{{drilldown.length.toFixed(4)}}</td>
                                <td>{{drilldown.width.toFixed(4)}}</td>
                                <td>{{(drilldown.receivedQuantity * item.unitPrice).toFixed(2)}}</td>
                                <td>{{(((drilldown.receivedQuantity * item.unitPrice)*item.taxRate)/100).toFixed(2)}}</td>
                            </tr>
                            </tbody>


                        </table>
                    </td>

                </tr>
                </tbody>
            </table>
        </div>
        <div class="TableMobileView">
            <ul class="collection striped center">
                <li class="collection-item" data-ng-repeat="item in sr.serviceReceiveItems">
                    <div class="row">
                        <div class="col">Cost Center</div>
                        <div class="col">{{item.businessCostCenterName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Cost Element</div>
                        <div class="col">{{item.costElementName}}[{{item.ascCode}}]</div>
                    </div>
                    <div <div class="row">
                    <div class="col">Service Description</div>
                    <div class="col" style="word-break: break-all;">{{item.serviceDescription}}</div>
                </div>
                    <div <div class="row">
                    <div class="col">Unit Price</div>
                    <div class="col">{{item.unitPrice.toFixed(2)}}</div>
                </div>
                    <div <div class="row">
                    <div class="col">Quantity</div>
                    <div class="col">{{item.receivedQuantity}}</div>
                </div>
                    <div <div class="row">
                    <div class="col">Tax Rate</div>
                    <div class="col">{{item.taxRate.toFixed(2)}}</div>
                </div>
                    <div <div class="row">
                    <div class="col">Cost</div>
                    <div class="col">{{item.totalCost.toFixed(2)}}</div>
                </div>
                    <div <div class="row">
                    <div class="col">Tax</div>
                    <div class="col">{{item.totalTax.toFixed(2)}}</div>
                </div>
                </li>
            </ul>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{billAmount.toFixed(2)}}<br/>
            <b>Total Taxes:</b> Rs.{{totalTaxes.toFixed(2)}}<br/>
            <b>Paid Amount:</b> Rs.{{paidAmount.toFixed(2)}}
        </div>
        <div class="row right-align margin-top-10 " >
            <button data-ng-if="sr.status=='PROVISIONAL' && sr.paymentRequestId==null"
                    class="btn btn-medium "  style="width: 150px; !important;" acl-action="SRAPPRV"
                    data-ng-click="approveSR(sr.id)">APPROVE</button>
        </div>
    </div>
</script>


<!-- View SR Detail for PR Modal -->
<script type="text/ng-template" id="viewSRsDetail.html">
    <div class="row margin0" data-ng-init="initViewModal()" style="max-height: 500px !important; overflow-y: auto;">
        <h5>List of Services under PR#{{pr.paymentRequestId}} raised for {{companyMap[pr.companyId].name}}</h5>
        <ul class="row margin0" data-ng-repeat="sr in srs" data-collapsible="accordion" watch>
            <li class="row margin0">
                <label class="collapsible-header waves-light" style="background: #d8e416; border:1px solid #f3c4c4;">
                    SR# {{sr.id}} issued for {{sr.company.name}} {{sr.deliveryState.name}}
                </label>
                <div class="collapsible-body standardView">
                    <table class="bordered striped">
                        <thead>
                        <tr>
                            <th>Cost Center</th>
                            <th>Cost Element</th>
                            <th>Service Description</th>
                            <th>Unit Price</th>
                            <th>Quantity</th>
                            <th>Tax Rate</th>
                            <th>Cost</th>
                            <th>Tax</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="item in sr.serviceReceiveItems">
                            <td>{{item.businessCostCenterName}}</td>
                            <td>{{item.costElementName}}[{{item.ascCode}}]</td>
                            <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.receivedQuantity}}</td>
                            <td>{{item.taxRate.toFixed(2)}}</td>
                            <td>{{item.totalCost.toFixed(2)}}</td>
                            <td>{{item.totalTax.toFixed(2)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </li>
        </ul>
    </div>
    <div class="TableMobileView">
        <ul class="collection striped center">
            <li class="collection-item" data-ng-repeat="item in sr.serviceReceiveItems">
                <div class="row">
                    <th>Cost Center</th>
                    <td>{{item.businessCostCenterName}}</td>
                </div>
                <div class="row">
                    <th>Cost Element</th>
                    <td>{{item.costElementName}}[{{item.ascCode}}]</td>
                </div>
                <div class="row">
                    <th>Service Description</th>
                    <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                </div>
                <div class="row">
                    <th>Unit Price</th>
                    <td>{{item.unitPrice.toFixed(2)}}</td>
                </div>
                <div class="row">
                    <th>Quantity</th>
                    <td>{{item.receivedQuantity}}</td>
                </div>
                <div class="row">
                    <th>Tax Rate</th>
                    <td>{{item.taxRate.toFixed(2)}}</td>
                </div>
                <div class="row">
                    <th>Cost</th>
                    <td>{{item.totalCost.toFixed(2)}}</td>
                </div>
                <div class="row">
                    <th>Tax</th>
                    <td>{{item.totalTax.toFixed(2)}}</td>
                </div>
            </li>
        </ul>
    </div>
</script>


<!-- View Detail Modal -->
<script type="text/ng-template" id="viewSODetail.html">
    <div class="row" data-ng-init="initViewModal()">
        <h5 style="text-align-last: center;">SO# {{so.id}} issued for {{so.vendor.name}}
            {{so.dispatchLocation.city}}</h5>
        <div class="row standardView" style="max-height: 500px; overflow: auto;">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th>Cost Element</th>
                    <th>BCC</th>
                    <th>Service Description</th>
                    <th>Unit Of Measure</th>
                    <th>Unit Price</th>
                    <th>Requested Quantity</th>
                    <th>Tax Rate</th>
                    <th>Cost</th>
                    <th>Tax</th>
                    <th>Amount</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in so.orderItems">
                    <td>{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
                    <td>{{item.businessCostCenterName}}</td>
                    <td style="word-break: break-all;">{{item.serviceDescription}}</td>
                    <td>{{item.unitOfMeasure}}</td>
                    <td>{{item.unitPrice}}</td>
                    <td>{{item.requestedQuantity}}</td>
                    <td>{{item.taxRate}}</td>
                    <td>{{item.totalCost.toFixed(2)}}</td>
                    <td>{{item.totalTax.toFixed(2)}}</td>
                    <td>{{item.amountPaid.toFixed(2)}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="TableMobileView">
            <ul class="collection striped center">
                <li class="collection-item" data-ng-repeat="item in so.orderItems">
                    <div class="row">
                        <div class="col">Cost Element</div>
                        <div class="col">{{item.costElementName}}[{{item.ascCode}}]({{item.costElemendivate| date
                            :'dd-MM-yyyy'}})</div>
                    </div>
                    <div class="row">
                        <div class="col">BCC</div>
                        <div class="col">{{item.businessCostCenterName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Service Description</div>
                        <div class="col" style="word-break: break-all;">{{item.serviceDescription}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Unit Of Measure</div>
                        <div class="col">{{item.unitOfMeasure}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Unit Price</div>
                        <div class="col">{{item.unitPrice}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Requested Quantity</div>
                        <div class="col">{{item.requestedQuantity}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Tax Rate</div>
                        <div class="col">{{item.taxRate}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Cost</div>
                        <div class="col">{{item.totalCost.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Tax</div>
                        <div class="col">{{item.totalTax.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Amount</div>
                        <div class="col">{{item.amountPaid.toFixed(2)}}</div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{billAmount.toFixed(2)}}<br/>
            <b>Total Taxes:</b> Rs.{{totalTaxes.toFixed(2)}}<br/>
            <b>Paid Amount:</b> Rs.{{paidAmount.toFixed(2)}}
            <div data-ng-if="so.status=='PENDING_APPROVAL_L1'">
                <b>SO Generated by:</b> {{so.lastUpdatedBy.name}} - {{so.lastUpdatedBy.id}}
            </div>
            <div data-ng-if="so.status!='PENDING_APPROVAL_L1'">
                <b>Last Approved by:</b> {{so.lastUpdatedBy.name}} - {{so.lastUpdatedBy.id}}
            </div>
        </div>
        <!-- Add floating action buttons -->
        <div class="floating-action-buttons">
            <!-- Download HOD Approval button -->
            <button class="btn waves-effect waves-light blue" 
                    data-ng-if="so.approvalOfHodDocumentId != null" 
                    data-ng-click="downloadDocumentById(so.approvalOfHodDocumentId)">
                <i class="material-icons">file_download</i> Download HOD
            </button>

            <!-- Download Document button -->
            <button class="btn waves-effect waves-light blue" 
                    data-ng-if="so.uploadedDocumentId != null" 
                    data-ng-click="downloadDocumentById(so.uploadedDocumentId)">
                <i class="material-icons">file_download</i> Download Doc
            </button>
           

              <!-- Cancel button -->
              <button class="btn waves-effect waves-light grey" 
              data-ng-click="cancel()">
              <i class="material-icons">close</i> Cancel
              </button>
            

            <!-- Reject button -->
            <button data-ng-show="check && !status" 
                    class="btn waves-effect waves-light red" 
                    data-ng-click="submit()">
                <i class="material-icons">cancel</i> Reject
            </button>

            <!-- Approve button -->
            <button data-ng-show="check && status" 
                    class="btn waves-effect waves-light green" 
                    data-ng-click="submit()">
                <i class="material-icons">check_circle</i> Approve
            </button>

          
        </div>
    </div>
</script>
